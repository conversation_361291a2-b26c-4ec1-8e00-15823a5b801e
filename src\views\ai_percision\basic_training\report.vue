<template>
    <div class="container" v-loading="reportState.loading">
      <div class="left">
        <div class="test-header">
            <!-- queryData.source -->
            <!-- <div class="blue-btn" @click="showDialog" v-if="isShow !== 1 ">
                查看测评报告
            </div> -->
            <div class="gery-bg-text" v-if="isShow !== 1">
                <img src="@/assets/img/percision/report.png" alt="report" >此次练习正确率：<span>{{reportState.correctRate}}%</span>
            </div>
            <div class="switch-box">
                <el-switch size="small" v-model="reportState.switch" /> <span>只看错题 </span>
            </div>
        </div>
        <div class="test-content">
            <div v-for="(item, index) in allTest" :key="item.quesId">
                <div class="test-content-ques" v-if="reportState.switch?(item.userMark != 1?true:false):true" :class="setClass(item, index)" >
                    <div class="squre"></div>
                    <div class="test-tittle">
                        <div v-html="resetSty(item, index + 1)" />
                    </div>
                    <div class="test-body" v-html="resetOptions(item)" />
                    <div class="show-analyse">
                        <el-switch size="small" @change="togAnswer(item,item.showAnalyse)" v-model="item.showAnalyse" /> <span>显示答案与解析</span>
                    </div>
                    <div v-show="item.showAnalyse" class="analyse">
                        <div class="flex-sty" style="cursor: pointer;" @click="toPoint(item)">
                            <span>【知识点】</span>&nbsp;&nbsp;&nbsp;&nbsp;
                            <img class="bfan" src="@/assets/img/percision/training/bfan.png" alt="">
                            <div style="color: rgba(0, 156, 127, 1);display: inline;border-bottom: 1px solid rgba(0, 156, 127, 1);" v-html="item.ques.pointVos[0].name" />
                        </div>
                        <div class="flex-sty">
                            <span>【答案】</span>&nbsp;&nbsp;
                            <div v-html="item.ques.displayAnswer" />
                        </div>
                        <div class="flex-sty">
                            <span>【分析】</span>&nbsp;&nbsp;
                            <div v-html="item.ques.analyse" />
                        </div>
                        <div class="flex-sty">
                            <span>【解答】</span>&nbsp;&nbsp;
                            <div v-html="item.ques.method" />
                        </div>
                        <div class="flex-sty">
                            <span>【点评】</span>&nbsp;&nbsp;
                            <div v-html="item.ques.discuss" />
                        </div>
                    </div>
                    <div v-if="item.userAnswer && item.userAnswer.length > 0">
                        <div v-if="item.ques.cate == 1 || item.ques.cate == 3" class="answers">
                            <div v-for="(it,ind) in item.ques.options" :class="item.userAnswer.includes(ind.toString())?(item.ques.answers?.includes(ind.toString())?'selected':'wrong-selected'):''" class="answer-box">
                                <div></div>{{ String.fromCharCode(65 + ind) }}
                            </div>
                        </div>
                        <div v-else>
                            <div class="answer-img-box" v-if="item.userAnswer.length > 0">
                                <el-image
                                    class="answer-img"
                                    v-for="(it, ind) in item.userAnswer"
                                    style="width: 10.8125rem; height: 10.8125rem;border-radius: .25rem;"
                                    :src="it"
                                    :zoom-rate="1.2"
                                    :max-scale="7"
                                    :min-scale="0.2"
                                    :preview-src-list="item.userAnswer"
                                    show-progress
                                    :initial-index="ind"
                                    fit="cover"
                                />
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
      </div>
      <div class="right">
        <div class="time-box">
            <div class="time-text">用时:</div> {{secondsToHMS( recordTime) }}
            <div class="time-number"> {{ timeState.hours }} </div> :
            <div class="time-number"> {{ timeState.minutes }} </div> :
            <div class="time-number"> {{ timeState.seconds }} </div>
        </div>
        <div class="test-number-box">
            <div class="test-number-item" v-for="(item, index) in allTest" :class="setClass1(item, index)"> {{ index + 1 }} </div>
            <!-- 按钮显示 -->
            <div class="infor">
                <!-- 如果有未过关的题目，只显示错题消化按钮 -->
                <img v-if="queryData.source !=='ripe' && queryData.learn == 'record'" @click.stop="onReport" style="width: 50px;height: 50px;position: absolute;bottom: 80px;right: 0px;" src="@/assets/img/percision/training/baogao.png" alt="">
                <div v-if="hasUnpassedItems" class="digestion" @click="handleDigestion" >
                    <span>错题消化</span>
                </div>
                <!-- 如果所有题目都已过关且可以挑战下一等级，显示挑战按钮和返回按钮 -->
                <template v-else-if="allPassedAndCanChallenge">
                    <div class="digestion" @click="handleChallengeNextLevel">
                        <span>{{ nextChallengeLevelName }}</span>
                    </div>
                    <div class="digestion" @click="handleGoBack">
                        <span>返回</span>
                    </div>
                </template>
                <!-- 其他情况显示返回按钮 -->
                <div v-else class="digestion" @click="handleGoBacks">
                    <span>返回</span>
                </div>
                    <!-- 右下角报告展示区域 -->
                <div v-if="reportVisible" class="report-panel">
                <div class="report-panel-header">
                    <div class="close-btn" @click="reportVisible = false">×</div>
                </div>
                
                <div class="report-panel-content" style="display: none;">
                    <!-- 知识点列表 -->
                    <div class="knowledge-list">
                    <div 
                        v-for="(item, index) in pointItems" 
                        :key="index" 
                        class="knowledge-item"
                    >
                        <div class="knowledge-info">
                            <div class="knowledge-name">{{ index + 1 }}.{{ item.pointName }}</div>
                            <div class="knowledge-stats">
                                <span class="knowledge-rate">{{ parseFloat((Number(item.correctRate)).toFixed(2)) }}%</span>
                                <span class="knowledge-status" :class="{ 'status-passed': item.passingStatus === '已过关', 'status-failed': item.passingStatus === '未过关' }">
                                {{ item.passingStatus }}
                                </span>
                            </div>
                            <div class="knowledge-actions" >
                                <div v-if="item.passingStatus === '未过关'" class="action-btn study-btn" @click="goLearning(item)">
                                    <img src="@/assets/img/percision/training/bbfan.png" alt="">
                                    <span>针对学习</span>
                                </div>
                            </div>
                            <div class="knowledge-level">
                                <span>当前等级：</span>
                                <span>{{ getLevelInfo(item.level).name }}</span>
                                <img :src="getLevelInfo(item.level).image" alt="" class="level-icon">
                            </div>
                        </div>
                    </div>    
                    </div>
                </div>
                </div>
            </div>
        </div>      
      </div>
      <!-- {{ queryData.showStep }} -->
      <div class="five-step-box" v-if="queryData.showStep && isShow !== 1">
        <fiveStep :sourceId="queryData.sourceId" :type="queryData.type==='synchronous'?1:2" :update="true" @sendStep="sendStep"></fiveStep>
      </div>
    </div>

    <el-dialog
        v-if="queryData.source !=='training'"
        v-model="dialogState.visible2"
        :show-close="false"
        top="8vh"
        :class="{'weekness-dialog3': queryData.source === 'ripe'}"
        class="weekness-dialog2 "
    >
        <div v-if="dialogState.showGif" class="gif-box">
            <img class="fail-gif" src="@/assets/img/percision/fail_gif.gif" />
            <img class="fail-text-png" src="@/assets/img/percision/fail_text.png" />
            <!-- <div class="fail_gif" id="lottie3"></div> -->
            <!-- 上移动画 -->
            <div class="jf_num showup">
                <img src="@/assets/img/layout/integral.png" />
                + {{ '4' }}
            </div>
            <!-- <img class="success-gif" src="@/assets/img/percision/final_success.gif" />
            <img class="text-png" src="@/assets/img/percision/final_win_text.png" /> -->
        </div>
        <div v-else class="dialog-box">
            <!-- 统计信息居中显示 -->
            <!-- <div class="summary-text">
                共 {{ pointItems.length }} 个知识点，{{ reportState.quesCount - reportState.correct }} 道题需要订正
            </div> -->
            <div class="data-box" style="justify-content: center;">
                 <!-- <div class="data-box-item" v-if="queryData.source === 'ripe'">
                    <img src="@/assets/img/percision/training/zsd.png" />
                    测评知识点数量：<span>{{ pointItems.length }}{{ pointItems }}</span>
                </div> -->
                <div class="data-box-item">
                    <img src="@/assets/img/percision/answer-num.png" />
                    答题数：<span>{{ reportState.quesCount }}</span>
                </div>
               
                <div class="data-box-item">
                    <img src="@/assets/img/percision/report.png" />
                    正确率：<span>{{ reportState.correctRate }}%</span>
                </div>
                <div class="data-box-item">
                    <img src="@/assets/img/percision/jifen.png" />
                        <div class="time-text">答题时长: </div> {{secondsToHMS( reportState.trainTime) }}
                        <div class="time-number"> {{ timeState.hours }} </div> :
                        <div class="time-number"> {{ timeState.minutes }} </div> :
                        <div class="time-number"> {{ timeState.seconds }} </div>

                </div>
                <!-- <div class="data-box-item">
                    <img src="@/assets/img/percision/jifen.png" />
                    规定答题时长：<span>{{ reportState.integral }}分</span>
                </div> -->
            </div>
            <div class=" item-list">
                <div class="list" v-for="(item,index) in pointItems" :key="index">
                    <div class="name">{{ item.pointName }}</div>
                    <div class="accuracy">正确率：<span style="color: rgba(42, 43, 42, 1);font-weight: 600;">{{ parseFloat((Number(item.correctRate)).toFixed(2))}} %</span> </div>
                    <img class="pass" v-if="item.passingStatus=='未过关'" src="@/assets/img/percision/training/wgg.png" alt="">
                    <img class="pass" v-else src="@/assets/img/percision/training/ywc.png" alt="">
                    
                    <div class="learn" @click="goLearning(item)">
                        <img src="@/assets/img/percision/training/bbfan.png" alt="">
                        <span>针对学习</span>
                    </div>
                                         <div class="learns">
                         <span>当前等级:</span>
                         <span style="color: rgba(42, 43, 42, 1);font-weight: 600;">{{ getLevelInfo(item.level).name }}</span>
                         <img :src="getLevelInfo(item.level).image" alt="">    
                     </div>
                </div>
            </div>
            <div class="center-flex">
                <div class="handle-table">
                    <div class="first-row">
                        <div class="first-row-item blue-item"> 题号</div>
                        <div class="first-row-item" v-for="(ind, index) in quesItems" :key="index">{{ ind.quesNum }}</div>
                    </div>
                    <div class="second-row">
                        <div class="second-row-item blue-item"> 做题时长</div>
                        <div class="second-row-item" v-for="(ind, index) in quesItems" :key="index">{{ Number(ind.trainTime) / 1000}}s
                            <div v-if="ind.isTimeout" style="width: 48px;line-height: 26px;height: 26px;background: rgba(221, 42, 42, 1);color: #fff;border-radius: 4px;text-align: center;margin-left: 8px;font-size: 16px;">超时</div>
                        </div>
                    </div>
                                         <div class="first-row">
                         <div class="second-row-item blue-item"> 答题情况</div>
                         <div class="second-row-item status-icon" 
                              v-for="(ind, index) in quesItems" 
                              :key="index"
                              :class="getAnswerStatusClass(ind.answeringStatus)">
                              {{ ind.answeringStatus }}
                         </div>
                     </div>
                </div>
            </div>
            <!-- <div class="center-flex padtop">
                <EchartsComponent :width="'350px'" :height="'260px'" :option="options"></EchartsComponent>
                <div class="progress-box2">
                    <el-progress type="circle" color="#00C9A3" :stroke-width="20" :percentage="reportState.percentage">
                        <template #default="{ percentage }">
                            <span class="percentage-value24">{{ reportState.correct }}</span> <span class="percentage-value14"> / {{ reportState.quesCount }}</span>
                            <div class="percentage-label">答题情况</div>
                        </template>
                    </el-progress>
                </div>
            </div> -->
        </div>
        <img class="close-icon" @click="dialogState.visible2 = false" src="@/assets/img/percision/dialog-close.png" />
        <template #footer>

        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { watch, onMounted, reactive, ref, onUnmounted, nextTick, computed } from 'vue'
import {  addTrainingApi, trainingInfoApi} from "@/api/precise"
import { dataDecrypt, getDegreeName,dataEncrypt } from "@/utils/secret"

import { useRouter, useRoute } from 'vue-router'
import EchartsComponent from "@/components/echarts/index.vue"
import fiveStep from "@/views/components/fiveStep/index.vue"
import {  getDetailssApi, } from '@/api/training'
// import { animationData } from '@/utils/lottie/final_fail'
import { options } from "./data"
// import { options } from "@views/ai_percision/basic_training/data"
import { quesGetApi} from "@/api/video"
import {  detailsToPointApi} from "@/api/precise"
import { el } from 'element-plus/es/locale'

const route = useRoute()
const router = useRouter()
const timeState = reactive({
    hours: 0,
    minutes: 0,
    seconds: 0
})

const reportState = reactive({
    step: 1,
    switch: false,
    loading: false,
    correctRate: 0,
    quesCount: 0,
    percentage: 0,
    integral: 0,
    correct: 0,
    trainingId: "",
    trainTime:0
})
const dialogState = reactive({
    visible: false,
    visible2: false,
    showGif: false
})
interface Ques {
    cate: number;
    cateName: string;
    content: string;
    displayAnswer: string;
    analyse: string;
    method: string;
    discuss: string;
    options: any[];
    pointVos: any[];
    userJson: any[];
    answers: any[];
}

interface PointItem {
    pointName: string;
    passingStatus: string;
    level: number | string;
    correctRate: number;
    pointId?: string; // 知识点ID字段
    id?: string; // 备用ID字段
}

interface QuesItem {
    quesNum: number | string;
    trainTime: number | string;
    answeringStatus: number | string;
    isTimeout: boolean;
}

class AData {
    quesId: string = "";
    cate: number = 0;
    cateName: string = "";
    trainTime: string = "";
    userAnswer: string[] = [];
    userMark: number | null = null;
    showAnalyse: boolean = false;
    content: string = "";
    ques: Ques = { // 添加 ques 属性
        cate: 0,
        cateName: "",
        content: "",
        analyse: "",
        discuss: "",
        method: "",
        displayAnswer: "",
        options: [],
        pointVos: [],
        userJson: [],
        answers: []
    };
}
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
const allTest = ref<AData[]>([])
const trainingId = ref()
const pointItems = ref<PointItem[]>([])
const knowledgePoint = ref()
const pointData = ref([])
const quesItems = ref<QuesItem[]>([])
const isShow = queryData.isShow
const recordTime =ref()

// 自定义返回方法
const customGoBack = () => {
     
    if(queryData.learn=='record'){
        router.push({
        path: '/ai_percision/basic_training',
        query: {
            bookId:queryData.bookId,
            type:queryData.type,
            subject: queryData.subject,
        }
    })
    }else{
        router.go(-2)
    }
}

onMounted(() => {
    console.log(queryData,"queryDataqueryDataqueryDataDA 打印")
    showDialog()
    getDetails()
     // 注册自定义返回方法
    window.customGoBack = customGoBack
})
const toPoint =(val)=>{
    router.push({
        name: 'TeachRoomTeachVideo',
        query: {
            id: val.pointId,
            pointName: val.ques.pointVos[0].name,
            source: 'analysis',
            subject: queryData.subject,
        }
    })
}
const goLearning =(val)=>{
    router.push({
    name: 'TeachRoomTeachVideo',
    query: {
        id: val.pointId,
        pointName: val.pointName,
        source: 'analysis',
        subject: queryData.subject,
    }
  })

}

// 处理错题消化按钮点击
const handleDigestion = () => {
    console.log('点击错题消化按钮',knowledgePoint.value)
    
    // 收集所有知识点ID
    const allPointIds: string[] = []
    pointItems.value.forEach((item: PointItem) => {
        // 优先使用pointId字段，如果没有则使用id字段
        const pointId = item.pointId || item.id
        if (pointId) {
            allPointIds.push(pointId)
        }
    })
    
    let source= '0'
    if(queryData.type=='1'){
        source='117'
    }else{
        source='118'
    }
    // 这里可以跳转到错题消化页面或执行相关逻辑
    router.push({
        path: '/ai_percision/wrongList',
        query: {
            source: source,
            subject:queryData.subject,
            subjectEn:queryData.subjectEn,
            allPointIds:allPointIds,
            curChapterId:queryData.curChapterId,
            num:allPointIds.length
        }
    })
}

// 处理挑战下一等级按钮点击
const handleChallengeNextLevel = () => {
    let nextLevel = 2 // 默认白银等级
    let levelName = '白银'
    
    if (queryData.type === '2') {
        // 提升训练：挑战钻石
        nextLevel = 4
        levelName = '钻石'
    } else {
        // 基础训练：挑战白银
        nextLevel = 2
        levelName = '白银'
    }
    
    // 收集所有知识点ID到chapterId数组
    const chapterId: string[] = []
    
    // 从pointItems中提取所有知识点ID
    if (pointItems.value && pointItems.value.length > 0) {
        pointItems.value.forEach((item: PointItem) => {
            // 假设知识点ID存储在pointId字段中，如果不是请调整字段名
            if (item.pointId) {
                chapterId.push(item.pointId)
            } else if (item.id) {
                // 如果ID字段名是id，则使用id
                chapterId.push(item.id)
            }
        })
    }
    
    // 如果pointItems中没有ID，尝试从allTest中获取知识点ID
    if (chapterId.length === 0 && allTest.value && allTest.value.length > 0) {
        allTest.value.forEach((item: any) => {
            if (item.ques && item.ques.pointVos && item.ques.pointVos.length > 0) {
                item.ques.pointVos.forEach((point: any) => {
                    if (point.id && !chapterId.includes(point.id)) {
                        chapterId.push(point.id)
                    }
                })
            }
        })
    }
    knowledgePoint.value= chapterId
    console.log(knowledgePoint.value,"knowledgePointknowledgePointknowledgePoin1111111111111111t")
    console.log(`点击挑战${levelName}按钮`)
    console.log('训练类型:', queryData.type)
    console.log('下一等级:', nextLevel-1)
    console.log('所有知识点ID (chapterId数组):', chapterId)
    console.log('知识点数量:', chapterId.length)
    console.log(queryData.bookId,"queryData.bookIdqueryData.bookIdqueryData.bookIdqueryData.bookId 123 123")
    // 获取id 接口
  addTrainingApi({
    bookId: queryData.bookId,
    pointIds:queryData.selectedChildIds?queryData.selectedChildIds:chapterId, // 直接使用当前课程的知识点ID
    subject:queryData.subject,
    chapterId:queryData.curChapterId,
    level: nextLevel // 动态传递挑战等级：1青铜 2白银 3黄金 4钻石

  }).then((res:any) => {
    if (res.code === 200) {
      if(res.data){
        router.push({
        path: '/ai_percision/answer_training',
        // path: '/ai_percision/entrance_assessment/doing_exercises',
        query: {
            data: dataEncrypt({
                reportId: res.data ,
                pageSource: '1',
                bookId: queryData.bookId,
                chapterId:queryData.curChapterId,
                level:nextLevel ,
                type:queryData.type,
                subject:queryData.subject,
                learn:'record',
            }),
        }
        })
           
    } 
  }
  })
}

// 处理返回按钮点击
const handleGoBack = () => {
    router.push({
      path: '/ai_percision/basic_training',
      query: {
          bookId:queryData.bookId,
          type:queryData.type,
          subject: queryData.subject,
        }
    })
}

// 处理标熟返回按钮点击
const handleGoBacks = () => {
    console.log(queryData.learn,"queryData.learnqueryData.learnqueryData.learn")
    if(queryData.learn=='record'){
        router.push({
        path: '/ai_percision/basic_training',
        query: {
            bookId:queryData.bookId,
            type:queryData.type,
            subject: queryData.subject,
        }
    })
    }else{
        router.push({
        path: '/ai_percision/knowledge_hotspot',
        query: {
            chapterId:queryData.chapterId,
            bookId:queryData.bookId,
            type:queryData.type,
            subject: queryData.subject,
            }
        })
    }

}

// 计算属性：判断是否有未过关的题目
const hasUnpassedItems = computed(() => {
    return pointItems.value.some(item => item.passingStatus === '未过关')
})
// 报告显示状态
const reportVisible = ref(false)

const onReport =() => {
    console.log("点击报告图片")
    // reportVisible.value = !reportVisible.value
    dialogState.visible2 = true
    // showDialog()
}
// 计算属性：判断是否所有题目都已过关且可以挑战下一等级
const allPassedAndCanChallenge = computed(() => {
    // 检查是否所有题目都已过关
    const allPassed = pointItems.value.length > 0 && pointItems.value.every(item => item.passingStatus === '已过关')
    
    if (!allPassed) return false
    
    // 根据训练类型判断是否可以挑战下一等级
    if (queryData.type === '1') {
        // 基础训练：检查是否有青铜等级的题目（可以挑战白银）
        return pointItems.value.some(item => Number(item.level) === 1)
    } else if (queryData.type === '2') {
        // 提升训练：检查是否有黄金等级的题目（可以挑战钻石）
        return pointItems.value.some(item => Number(item.level) === 3)
    }
    
    return false
})

// 计算属性：获取下一挑战等级的名称
const nextChallengeLevelName = computed(() => {
    if (queryData.type === '2') {
        return '挑战钻石'
    } else {
        return '挑战白银'
    }
})

// 获取答题状态的CSS类名
const getAnswerStatusClass = (status: string | number) => {
    if (status === '正确' || status === 1 || status === '对') {
        return 'status-correct'
    } else if (status === '错误' || status === 0 || status === '错') {
        return 'status-wrong'
    }
    return ''
}

// 获取等级信息（名称和图片）
const getLevelInfo = (level: number | string) => {
    const levelNum = Number(level)
    switch (levelNum) {
        case 1:
            return {
                name: '青铜',
                image: new URL('@/assets/img/percision/training/qingt.png', import.meta.url).href
            }
        case 2:
            return {
                name: '白银',
                image: new URL('@/assets/img/percision/training/baiyin.png', import.meta.url).href
            }
        case 3:
            return {
                name: '黄金',
                image: new URL('@/assets/img/percision/training/huangjin.png', import.meta.url).href
            }
        case 4:
            return {
                name: '钻石',
                image: new URL('@/assets/img/percision/training/zuanshi.png', import.meta.url).href
            }
        default:
            return {
                name: '青铜',
                image: new URL('@/assets/img/percision/training/qingt.png', import.meta.url).href
            }
    }
}

onUnmounted(() => {

// 清除自定义返回方法
 if (window.customGoBack) {
    delete window.customGoBack
}
})
const getStatus = (status: number) => {
    switch (status) {
        case 1:
            return "已掌握"
        case 2:
            return "一般"
        case 3:
            return "未掌握"
        default:
            return ""
    }
}
const getStatusClass = (status: number) => {
    switch (status) {
        case 1:
            return "green-box"
        case 2:
            return "yellow-box"
        case 3:
            return "red-box"
        default:
            return ""
    }
}

  //显示答案
const togAnswer = async (item:any,isShow:any) => { 
    if(isShow){
        // 如果已经有完整的题目信息，直接显示，无需重复请求
        if (item.ques.analyse && item.ques.method && item.ques.discuss) {
            return
        }   
        try {
            // 添加加载状态，防止重复点击
            if (item.loading) return
            item.loading = true
            
            const response = await quesGetApi({id: item.ques.quesId}) as any
            
            if (response.code === 200 && response.data) {
                // 使用 Object.assign 来安全地合并数据，保留原有属性
                Object.assign(item.ques, response.data)
                
                // 确保必要的属性存在
                if (!item.ques.pointVos) {
                    item.ques.pointVos = []
                }
                if (!item.ques.options) {
                    item.ques.options = []
                }
                if (!item.ques.answers) {
                    item.ques.answers = []
                }
                console.log('题目详细信息已更新:', item.ques)
            } else {
                console.error('获取题目详细信息失败:', response)
                // 如果获取失败，关闭显示开关
                item.showAnalyse = false
                // 可以添加用户提示
            }
        } catch (error) {
            console.error('获取题目详细信息时发生错误:', error)
            // 发生错误时关闭显示开关
            item.showAnalyse = false
            // 可以添加用户提示
        } finally {
            // 清除加载状态
            item.loading = false
        }
    }

}
const onJum = (row: any) =>{
    // console.log(row,"打印一下")
    // router.push({
    //     path: '/ai_percision/knowledge_graph_detail/training_report',
    //     query: { 
    //         data: dataEncrypt({
    //             sourceId: queryData.sourceId,
    //             showStep: 1,
    //             type:queryData.type
    //         })
    //     }
    // })
}
const getUrl = (data: number) => {
    let url = "rise"
    if (data < 0) {
        url = "decline"
    }
    return new URL(`../../../assets/img/percision/${url}.png`, import.meta.url).href //静态资源引入为url，相当于require()
}
// 获取学习步骤
const sendStep = ( data: number) => {
    reportState.step = data
}
// 测评报告弹窗
const showDialog = () => {
    if(queryData.source == 'analysis'){
        dialogState.visible2 = true
    }else{
        dialogState.visible2 = false
    } 
    // if (queryData.pageSource == '11') {
    //     options.series[0].radius = ["65%","100%"]
    //     options.legend.show = false
    //     dialogState.visible2 = true
    //     dialogState.showGif = true
    //     setTimeout(() => {
    //         dialogState.showGif = false
    //     }, 2100)
    // } else {
    //     options.series[0].radius = ["35%","55%"]
    //     options.legend.show = true
    //     dialogState.visible = true
    // }
}
//失败动画
const failAnimation = () => {
    let window1 : any = window
    let lottie : any = window1?.lottie
    let params = {
      container: document.getElementById('lottie3'),
      renderer: 'svg',
      loop: false,
      autoplay: true,
    //   animationData: animationData
    };
    lottie.loadAnimation(params);
}
const getDetails = async() => {
    if(queryData.source == 'training'){
        await detailsToPointApi({
            bookId: queryData.bookId,
            pointId:queryData.pointId,
            type:queryData.type,
            chapterId:queryData.chapterId
        }).then((res:any) => {
            const data = res.data
            if (res.code === 200) {
                console.log(res.data.trainTime,"pointIdspointIdspointIdspointIds11232354351605")

                allTest.value = data.items.forEach((item) => {
                    item.showAnalyse = false
                })
                allTest.value = data.items
                // reportState.correctRate = parseFloat((Number(data.correctRate)).toFixed(2))
                reportState.correctRate = data.correctRate
                reportState.correct = Number(data.correct)
                reportState.quesCount = Number(data.quesCount)
                reportState.integral = Number(data.integral)
                reportState.percentage = Number((reportState.correct*100 / reportState.quesCount).toFixed(0))
                reportState.trainTime = Number(data.trainTime/1000 )
                recordTime.value = Number(data.trainTime/1000 )
                pointItems.value = data.reportJson.pointItems || []
                quesItems.value = data.reportJson.quesItems || []
                dialogState.visible2 = true
                if (queryData.pageSource == '11') {
                    dialogState.visible2 = true
                }
            }
        })

    }else{
        await getDetailssApi({trainingId: queryData.reportId}).then((res1: any) => {
        const data = res1.data
        if (res1.code == 200) {
                allTest.value = data.items.forEach((item) => {
                item.showAnalyse = false
                    
            })
            allTest.value = data.items
            reportState.correctRate = parseFloat((Number(data.correctRate)).toFixed(2))
            reportState.correct = Number(data.correct)
            reportState.quesCount = Number(data.quesCount)
            reportState.integral = Number(data.integral)
            reportState.percentage = Number((reportState.correct*100 / reportState.quesCount).toFixed(0))
            reportState.trainTime = Number(data.trainTime/1000 )
            pointItems.value = data.reportJson.pointItems || []
            quesItems.value = data.reportJson.quesItems || []
            recordTime.value = Number(data.trainTime/1000 )
            // pointData.value = data.reportJson.pointJson || []
            dialogState.visible2 = true
            if (queryData.pageSource == '11') {
                dialogState.visible2 = true
            }
            } 
        })
    }


}

function secondsToHMS(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    // 补零操作，确保两位数显示
    const pad = (num) => num.toString().padStart(2, '0');
    timeState.seconds = pad(secs)
    timeState.minutes = pad(minutes)
    timeState.hours = pad(hours)

}

const setClass = (item: any, index: number) => {
    let classState = ""
    if (item.userMark != null) {
        if (item.userMark == 0) {
            classState = "red-border"
        } else if (item.userMark == 1) {
            classState = "green-border"
        } else if (item.userMark == 2) {
            classState = "yellow-border"
        }

    }
    return classState
}
const setClass1 = (item: any, index: number) => {
    let classState = ""
    if (item.userMark != null) {
        if (item.userMark == 0) {
            classState = "red"
        } else if (item.userMark == 1) {
            classState = "green"
        } else if (item.userMark == 2) {
            classState = "yellow"
        }

    }
    return classState
}
//过滤修改题干标签内容
const resetSty = function (testItem: any, sort?: number) {
  const tittle = "（" + testItem.ques.cateName + "）" +sort + "." + filterContent(testItem.ques.content)
  return tittle
}
//过滤掉填空题等div标签的contenteditable属性
const filterContent = (contect: string) => {
  let highlightedResult = ""
  highlightedResult = contect.replaceAll('contenteditable="true"', " ")
  highlightedResult = highlightedResult.replaceAll("contenteditable='true'", " ")
  return highlightedResult
}
//过滤修改选项内容
const resetOptions = function (testItem: any) {
    let optionHtml = ""
    if (!testItem.ques.options) return
    testItem.ques.options.map((item: any, index: number) => {
        optionHtml += `<div class="answer-item"> ${String.fromCharCode(65 + index)}. <div style="display:inline-table;max-width:39.8125rem">${item}</div></div>`
    })
    return optionHtml
}
const toknowledgeDetail = () => {
    if(queryData.contentType == 'historyTask'){
        router.go(-2)

    }else{
        router.push({
        path: '/ai_percision/knowledge_graph_detail',
        query: {
            type:queryData.type,
            dataType:queryData.showStep
            }
        })
    }
    
}
// // 自定义返回方法
// const customGoBack = () => {
//      router.go(-1)
// }
// let timer :  NodeJS.Timeout | null = null
// onMounted(() => {
//     // 注册自定义返回方法
//     window.customGoBack = customGoBack
// timer = setInterval(() => {
// timeState.seconds ++
//  }, 1000)
// })
// onUnmounted(() => {
//  if (timer !== null) { // 添加类型安全检查
// clearInterval(timer)
// }
// // 清除自定义返回方法
//  if (window.customGoBack) {
// delete window.customGoBack
// }
// })
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    position: relative;
    .five-step-box {
        position: absolute;
        right: -8.75rem;
        top: 11.25rem;
    }
    .left {
        width: 60.3125rem;
        .test-header {
            width: 60.3125rem;
            height: 4.5rem;
            margin-bottom: .625rem;
            border: .0625rem solid #eaeaea;
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1.875rem;
            box-sizing: border-box;
            .gery-bg-text {
                padding: .375rem .75rem;
                border-radius: 1rem;
                background: #f5f5f5;
                color: #2a2b2a;
                font-size: .875rem;
                display: flex;
                align-items: center;
                img {
                    margin-right: .375rem;
                    width: 1rem;
                    height: 1rem;
                }
                span {
                    font-weight: 700;
                }
            }
            .switch-box {
                color: #666666;
                font-size: .75rem;
            }
        }
        .test-content {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 5.125rem);
            box-sizing: border-box;
            overflow-y: auto;
            .test-content-ques {
                background: #ffffff;
                width: 100%;
                box-sizing: border-box;
                padding: 1.25rem 0;
                margin-bottom: .625rem;
                position: relative;
                color: #999999;
                .show-analyse {
                    width: 100%;
                    background: #fef8e9;
                    padding-left: 1.875rem;
                    height: 2.1875rem;
                    box-sizing: border-box;
                    display: flex;
                    align-items: center;
                    color: #666666;
                    font-size: .75rem;
                    font-weight: 400;
                    margin-top: 1.25rem;
                    span {
                        margin-left: .375rem;
                    }
                }
                .squre {
                    width: .875rem;
                    height: 1rem;
                    border-radius: 0 .375rem .375rem 0;
                    background: #5a85ec;
                    position: absolute;
                    top: 1.625rem;
                    left: 0;
                }
                .test-tittle,.test-body {
                    padding: 0 1.875rem;
                }
            }
        }
    }
    .right {
        padding: 1.125rem 0;
        width: 20.3125rem;
        margin-left: .625rem;
        background: #ffffff;
        box-sizing: border-box;
        height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
        .time-box {
            display: flex;
            align-items: center;
            color: #2a2b2a;
            font-size: 1.875rem;
            font-weight: 700;
            padding-left: .625rem;
            padding-bottom: 1.25rem;
            border-bottom: .0625rem dashed #eaeaea;
            .time-text {
                font-size: 1rem;
                font-weight: 700;
            }
            .time-number {
                font-weight: 400;
                width: 3.75rem;
                height: 3.75rem;
                line-height: 3.75rem;
                text-align: center;
                border-radius: .25rem;
                border: .0625rem solid #eaeaea;
                background: #f5f5f5;
                margin: 0 .625rem;
            }
        }
        .test-number-box {
            position: relative;
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 8.125rem);
            box-sizing: border-box;
            padding: 1.25rem .625rem 1.25rem 0;
            .test-number-item {
                display: inline-block;
                margin-left: .625rem;
                margin-bottom: .625rem;
                border-radius: .25rem;
                width: 2.5rem;
                height: 2.5rem;
                line-height: 2.5rem;
                text-align: center;
                color: #2a2b2a;
                font-size: 1rem;
                font-weight: 400;
                border: .0625rem solid #eaeaea;
                background: #f5f5f5;
                box-sizing: border-box;
            }
            .blue {
                background: #5a85ec;
                color: #ffffff;
            }
            .red {
                background: #dd2a2a;
                color: #ffffff;
            }
            .green {
                background: #00c9a3;
                color: #ffffff;
            }
            .yellow {
                background: #f1be21;
                color: #ffffff;
            }
            .infor{
                position: absolute;
                bottom: 0;
                right: 20px;
                width: 90%;
                cursor: pointer;
                .digestion{
                    height: 44px;
                    background: rgba(0, 201, 163, 1);
                    color: #fff;
                    font-size: 16px;
                    font-weight: 700;
                    margin: 10px auto;
                    text-align: center;
                    line-height: 44px;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: background-color 0.3s ease;
                    
                    &:hover {
                        background: rgba(0, 180, 146, 1);
                    }
                    
                    span{
                        display: block;
                    }
                }
            }
            
        }
    }
}
.img-text {
    display: flex;
    align-items: center;
    justify-content: center;
    img {
        height: 1.4375rem;
        width: 1.125rem;
        margin-right: .625rem;
    }
}
.icon-btn {
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        margin-right: .3125rem;
    }
}
:deep(.el-checkbox-group) {
    .el-checkbox {
        width: 6.25rem;
        height: 3.125rem;
        margin-right: 1.25rem;
        display: inline-flex;
        justify-content: center;
    }
    .is-checked {
        .el-checkbox__inner {
            background: #5a85ec;
            border: .0625rem solid #5a85ec;
        }
        .el-checkbox__label {
            color: #5a85ec;
        }
    }
    .el-checkbox__inner {
        &:hover {
            border: .0625rem solid #5a85ec;
        }
    }
}
.analyse {
    padding: .625rem 1.875rem;
    letter-spacing: .125rem;
    background: #fef8e9;
    div {
      margin-bottom: .625rem;
    }
}
.flex-sty {
  display: flex;
  font-size: .875rem;
  align-items: baseline;
  position: relative;
  .bfan {
    position: absolute;
    top: 38%;
    left: 68px;
    width: 16px;
    height: 16px;
    transform: translateY(-50%);  // 使图片垂直居中
    margin-right: 6px;
   }
  div {
    max-width: 52.375rem;
    line-height: 1.0625rem;
  }
  span {
    text-wrap: nowrap;
    font-weight: 700;
    letter-spacing: normal;
  }
}
.paper-content-ques {
    margin-top: 1.25rem;
    border-top: .0625rem dashed #EAEAEA;
    padding: 1.25rem 1.875rem;
}
.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    text-align: center;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    margin-right: .625rem;
    font-weight: 400;
}
.black-text {
    color: black!important;
}
.red-border {
    border: .0625rem solid #dd2a2a;
    color: black!important;
    .squre {
        background: #dd2a2a!important;
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-border {
    color: black!important;
    border: .0625rem solid #00C9A3;
    .squre {
        background: #00C9A3!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.yellow-border {
    color: black!important;
    border: .0625rem solid #f1be21;
    .squre {
        background: #f1be21!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.center-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.answers {
    display: flex;
    margin-top: 1.25rem;
    .answer-box {
        width: 6.25rem;
        height: 3.125rem;
        border-radius: .25rem;
        border: .0625rem solid #dddddd;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.25rem;
        &:first-child {
            margin-left: 1.875rem;
        }
        div {
            border: .0625rem solid #999999;
            width: 1rem;
            height: 1rem;
            border-radius: .125rem;
            margin-right: .625rem;
        }
    }
}
.data-box {
    display: flex;
    padding-left: 2.5rem;
    // margin-top: 1.875rem;
    margin-bottom: 1.875rem;
    &-item {
        padding: .475rem 1rem;
        color: #2a2b2a;
        font-size: .875rem;
        font-weight: 400;
        border-radius: 1rem;
        background: #ffffff;
        margin-right: .625rem;
        display: flex;
        align-items: center;
        img {
            width: 1rem;
            height: 1rem;
        }
        span {
            font-size: 14px;
            font-weight: 700;
        }
    }
}
.item-list{
    width: 908px;
    margin: 0 auto;
    .list{

        display: flex;
        background: rgba(246, 248, 255, 1);
        margin-bottom: 10px;
        line-height: 47px;
        padding: 0 6px;
        border: 2px solid rgba(90, 133, 236, 1);
        font-size: 14px;
        align-items: center;
        border-radius: 10px;
        // justify-content: center;
        .name{
            color: rgba(90, 133, 236, 1);
            font-weight: 700;
            width: 35%;
            white-space: nowrap;           /* 禁止换行 */
            overflow: hidden;              /* 隐藏超出部分 */
            text-overflow: ellipsis;       /* 超出部分显示省略号 */
        }
        .accuracy{
            margin-left: 20px;
            font-size: 14px;
            color: rgba(42, 43, 42, 1);
        }
        .pass{
            margin-left: 30px;
            width: 60px;
            height: 31px;

        }
        .learn{
            margin-left: 30px;
            // justify-content: center;
            align-items: center;
            width: 96px;
            height: 29px;
            line-height: 29px;
            background: rgba(233, 139, 0, 1);
            border-radius: 14px;
            display: flex;
            cursor: pointer;
            img{
                width: 16px;
                height: 16px;
                margin-left: 9px;
                margin-right: 3px;
            }
            span{
                color: rgba(255, 255, 255, 1);
                font-size: 14px;
                line-height: 29px;
                display: block;
            }
        }
        .learns{
            margin-left: auto;
            align-items: center;
            display: flex;
            img{
                width: 36px;
                height: 30px;
            }
        }
    }
}
.table-box {
    cursor: pointer;
    width: 54.25rem;
    margin-left: 2.5rem;
    :deep(.el-table) {
        height: 12.5rem;
        .el-table__body tr:hover > td {
            background-color: transparent !important;
        }
        box-shadow: none;
        background-color: rgba(0, 0, 0, 0) !important;
        tr {
            background-color: rgba(0, 0, 0, 0) !important;
            &:hover {
                background-color: rgba(0, 0, 0, 0) !important;
            }
        }
        td {
            border: none;
        }
        th.el-table__cell {
            border: none;
            background-color: rgba(0, 0, 0, 0) !important;
            color: #132059;
            font-size: 1.25rem;
            font-weight: 700;
        }
        td.el-table__cell div{
            font-size: .875rem;
        }
    }
}
.table-status {
    width: 7.625rem;
    height: 1.4375rem;
    line-height: 1.4375rem;
    text-align: center;
    border-radius: 1.125rem;
    font-size: .875rem;
    font-weight: 400;
}
.echart-box {
    padding-left: 2.5rem;
    display: flex;
}
.progress-box {
    padding-top: 3.75rem;
    margin-left: 6.25rem;
    :deep(.el-progress) {
        .el-progress-circle {
            width: 9.125rem!important;
            height: 9.125rem!important;
        }
        path:first-child {
            stroke: #C5D0E1;
        }
        .el-progress__text {
            top: 4.375rem;
        }
    }
    .percentage-value14 {
        color: #1d1d1d;
        font-weight: 400;
        font-size: .875rem;
    }
    .percentage-value24 {
        color: #23cb89;
        font-weight: 700;
        font-size: 1.5rem;
    }
    .percentage-label {
        color: #2a2b2a;
        font-size: .875rem;
        font-weight: 700;
        margin-top: .625rem;
    }
    .legend-box {
        display: flex;
        justify-content: space-around;
        margin-top: 1.125rem;
        &-item {
            display: flex;
            align-items: center;
            color: #293853;
            font-size: .75rem;
            font-weight: 400;
            span {
                display: inline-block;
                width: .6875rem;
                height: .6875rem;
                border-radius: .1875rem;
                background: #00c9a3;
                margin-right: .3125rem;
            }
        }
        .grey {
            span {
                display: inline-block;
                width: .6875rem;
                height: .6875rem;
                border-radius: .1875rem;
                background: #c5d0e1;
            }
        }
    }
}
.padtop {
    padding-top: 40px;
}
.progress-box2 {
    margin-left: 6.25rem;
    :deep(.el-progress) {
        .el-progress-circle {
            width: 261px!important;
            height: 261px!important;
        }
        path:first-child {
            stroke: #C5D0E1;
        }
        .el-progress__text {
            top: 125px;
        }
    }
    .percentage-value14 {
        color: #1d1d1d;
        font-weight: 400;
        font-size: .875rem;
    }
    .percentage-value24 {
        color: #23cb89;
        font-weight: 700;
        font-size: 1.5rem;
    }
    .percentage-label {
        color: #2a2b2a;
        font-size: .875rem;
        font-weight: 700;
        margin-top: .625rem;
    }
}
.big-btn {
    margin-left: 9.375rem;
    margin-top: 11.875rem;
    width: 15.25rem;
    height: 3.5625rem;
    line-height: 3.5625rem;
    text-align: center;
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 700;
    letter-spacing: .0625rem;
    cursor: pointer;
    border-radius: 1.7813rem;
    background: linear-gradient(147.5deg, #36e2c2 0%, #00b7d0 100%);
    box-shadow: 0 .25rem .9375rem 0 #00000040;
}
.green-box {
    background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
    color: #ffffff!important;
}
.yellow-box {
    background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
    color: #ffffff!important;
}
.red-box {
    background: linear-gradient(150.8deg, #f07f4c 0%, #c95656 100%);
    color: #ffffff!important;
}
.center-flex {
    width: 908px;
    max-height: 438px; /* 6条数据的高度：6 * 73px = 438px */
    overflow-x: auto;
    overflow-y: auto;
    
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }
    
    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
        
        &:hover {
            background: #a8a8a8;
        }
    }
    
    /* Firefox 滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}
.handle-table {
    border: 1px solid #D1DAEF;
    border-right: none;
    min-width: 906px;
    width: max-content;
    .first-row {
        display: flex;
        min-width: max-content;
        div {
            border-right: 1px solid #D1DAEF;
            border-bottom: 1px solid #D1DAEF;
        }
        &-item {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 128px;
            width: 128px;
            height: 73px;
            background: #f6f8ff;
            color: #2a2b2a;
            font-size: 16px;
            font-weight: 400;
            flex-shrink: 0;
            flex: 1;
        }
    }
    .second-row {
        display: flex;
        min-width: max-content;
        div {
            border-right: 1px solid #D1DAEF;
        }
        &-item {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width:128px;
            width: 128px;
            height: 73px;
            background: #E3EEFF;
            color: #2a2b2a;
            font-size: 16px;
            font-weight: 400;
            flex-shrink: 0;
            flex: 1;
        }
    }
    .blue-item {
        color: #275dde;
        background: #B8D4FF;
        font-size: 16px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .status-icon{
        color: #275dde;
        background: rgba(246, 248, 255, 1);
        font-size: 16px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        &.status-correct {
            color: rgba(0, 156, 127, 1) !important;
        }
        &.status-wrong {
            color: rgba(221, 42, 42, 1) !important;
        }
    }
}
.fail_gif {
    width: 170px;
    height: 200px;
    overflow: hidden;
}
.jf_num {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-top: 80px;
    top: -50px;
    opacity: 0;
    width: 76px;
    height: 26px;
    border-radius: 13px;
    background: #00000066;
    color: #ffc000;
    font-size: 16px;
    font-weight: 700;
    img {
        width: 20px;
        height: 20px;
        margin-right: 8px;
    }
}

/* 上移动画 */
@keyframes showup {
    0% {
      top: 0;
      opacity: 0;
    }

    50% {
      top: -50px;
      opacity: 1;
    }

    100% {
      top: -50px;
      opacity: 1;
    }
}

.showup {
    -webkit-animation: showup 4s;
}

/* 右下角报告面板样式 */
.report-panel {
    position: absolute;
    bottom: 180px;
    right: 0px;
    width: 699px;
    // max-height: 128px;
    background: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    overflow: hidden;
    animation: slideInUp 0.3s ease;
    background: rgba(244, 247, 254, 1);
    // height: 128px;
    width: 700px;
    border-radius: 27px;
}

.report-panel-header {
    position: relative;
    
    .close-btn {
        position: absolute;
        top: 5px;
        right: 10px;
        cursor: pointer;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: bold;
        transition: background-color 0.2s;
        margin-left: auto;
        &:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    }
}

.report-panel-content {
    margin-top: 20px;
    max-height: calc(70vh - 60px);
    overflow-y: auto;
    padding: 0;
}


.knowledge-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    margin-top: 5px;
}

.knowledge-info {
    display: flex;
    padding: 0 46px 0 26px;
    margin-bottom: 26px;
    .knowledge-name {
        font-size: 14px;
        font-weight: 700;
        color: rgba(90, 133, 236, 1);
        margin-bottom: 6px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 230px;
        max-width: 250px;
    }
    
    .knowledge-stats {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        width: 132px;
        .knowledge-rate {
            font-size: 14px;
            font-weight: 700;
            
            .status-passed & {
                color: rgba(0, 201, 163, 1);
            }
            
            .status-failed & {
                color: rgba(221, 42, 42, 1);
            }
        }
        
        .knowledge-status {
            font-size: 14px;
            font-weight: 700;
            padding: 2px 8px;
            margin-right: 6px;
            
            &.status-passed {
                color: rgba(0, 201, 163, 1);
            }
            
            &.status-failed {
                color: rgba(221, 42, 42, 1);
            }
        }
    }
    
    .knowledge-level {
        display: flex;
        align-items: center;
        font-size: 11px;
        &:hover {
            background: #d16600;
            transform: scale(1.05);
        }
        
        img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }
        
    }
}

.report-panel-footer {
    padding: 16px 20px;
    border-top: 1px solid #eaeaea;
    background: #fafbfc;
    display: flex;
    justify-content: center;
    .footer-btn {
        padding: 10px 24px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        
        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        &.digestion-btn {
            background: #00c9a3;
            color: white;
            
            &:hover {
                background: #00a085;
            }
        }
        
        &.challenge-btn {
            background: linear-gradient(135deg, #5a85ec 0%, #4c73d9 100%);
            color: white;
            
            &:hover {
                background: linear-gradient(135deg, #4c73d9 0%, #3d5fb8 100%);
            }
        }
        
        &.back-btn {
            background: #666;
            color: white;
            
            &:hover {
                background: #555;
            }
        }
    }
}

/* 滑入动画 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .report-panel {
        width: calc(100vw - 40px);
        right: 20px;
        max-height: 60vh;
    }
    
    .knowledge-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .knowledge-info .knowledge-name {
        max-width: 100%;
    }
    
    .knowledge-actions {
        align-self: flex-end;
    }
}
</style>
<style lang="scss">
.weekness-dialog {
    cursor: pointer;
    width: 68.1875rem;
    padding: .625rem;
    height: 51.375rem;
    background-color: rgba(0, 0, 0, 0) !important;
    background-image: url(@/assets/img/percision/weekness-bg.png);
    background-repeat: no-repeat;
    background-size: 68.1875rem 49.25rem;
    border: none;
    box-shadow: none;
    position: relative;
    .close-icon {
        cursor: pointer;
        position: absolute;
        width: 3.125rem;
        height: 3.125rem;
        left: 45%;
        bottom: 0rem;
    }
    .el-dialog__header {
        display: none;
    }
    .dialog-box {
        .top-title {
            display: flex;
            align-items: center;
            margin-top: 6.625rem;
            margin-left: 3.9375rem;
            img {
                width: 2.5625rem;
                height: 2.8125rem;
                margin-right: .8125rem;
            }
            span {
                color: #132059;
                font-size: 2.5rem;
                font-weight: 700;
            }
        }
    }
}

.weekness-dialog2 {
    max-width: none;
    max-height: none;
    margin: 0 auto;
    padding: 0;
    background-color: rgba(0, 0, 0, 0) !important;
    background-image: url(@/assets/img/percision/training/tzbgs.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center center;
    border: none;
    box-shadow: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1090px;
    height: 660px;
    max-width: 95vw;
    max-height: 95vh;
    z-index: 9999;
    overflow: visible;
    display: flex;
    align-items: center;
    justify-content: center;
    .close-icon {
        position: absolute;
        width: 3.125rem;
        height: 3.125rem;
        left: 50%;
        bottom: -64px;
        transform: translateX(-50%);
        cursor: pointer;
        z-index: 10000;
    }
    .el-dialog__header {
        display: none;
    }
    .gif-box {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .success-gif {
            width: 600px;
            height: 342px;
        }
        .text-png {
            width: 411px;
            height: 26px;
        }
        .fail-gif {
            width: 300px;
            height: 300px;
        }
        .fail-text-png {
            width: 100px;
            height: 26px;
        }
    }
    .dialog-box {
        width: 100%;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 30px;
        
        .summary-text {
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            color: #2a2b2a;
            margin-bottom: 20px;
            padding: 10px 20px;
            background: rgba(0, 201, 163, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(0, 201, 163, 0.3);
        }
        
        .data-box {
            // margin-top: 40px;
        }
    }
}
.weekness-dialog3{
    background-image: url(@/assets/img/percision/training/jcbgs.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center center;
}
.answer-item:not(:last-child) {
    margin-bottom: 1.875rem;
}
</style>
