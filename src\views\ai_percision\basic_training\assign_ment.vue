<template>
    <div class="container" v-loading="writeState.loading">
    <div style="width: 1300px;margin: 0 auto;">
            <div class="top-nav">
                <img class="exit-btn"  @click="goBack" src="@/assets/img/percision/training/exit.png" alt="">
                <!-- 🎯 新增：答题进度条 -->
                <div class="progress-container">
                    <div class="progress-bg"></div>
                    <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                    <img class="progress-icon" :style="{ left: progressPercentage + '%' }" src="@/assets/img/percision/training/move.gif" alt="进度">
                </div>
            </div>
    <div class="oll" style="display: flex;width: 100%;border-radius: 0 20px 20px 0;">
      <div class="left">
        <div class="test-content">
                        <!-- 加载状态 -->
                        <div v-if="writeState.loading" class="loading-container">
                            <div class="loading-content">
                                <el-icon class="loading-icon"><Loading /></el-icon>
                                <p class="loading-text">正在加载训练数据...</p>
                            </div>
                        </div>
                        
                        <!-- 无数据状态 -->
                        <div v-else-if="!allTest.length" class="empty-container">
                            <div class="empty-content">
                                <el-icon class="empty-icon"><Document /></el-icon>
                                <p class="empty-text">暂无训练题目</p>
                                <el-button @click="getDetails" type="primary">重新加载</el-button>
                            </div>
                        </div>
                        
                        <!-- 🎯 优化：移除全部完成状态显示，点击完成后继续显示答题内容 -->
                        <!-- <div v-else-if="writeState.current === null && allTest.length > 0" class="completed-container">
                            <div class="completed-content">
                                <el-icon class="completed-icon"><SuccessFilled /></el-icon>
                                <h3 class="completed-title">恭喜！所有题目已完成</h3>
                                <p class="completed-text">你已经完成了全部 {{ allTest.length }} 道题目</p>
                                <div class="completed-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">用时：</span>
                                        <span class="stat-value">{{ timeState.hours < 10 ? "0" + timeState.hours : timeState.hours }}:{{ timeState.minutes < 10 ? "0" + timeState.minutes : timeState.minutes }}:{{ timeState.seconds < 10 ? "0" + timeState.seconds : timeState.seconds }}</span>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        
            <!-- 一题一页显示 -->
            <div v-else-if="writeState.current !== null && allTest[writeState.current]" class="single-question-container">
                <div class="question-cont">
                    <div class="question-header">
                        <span class="question-type">{{ allTest[writeState.current].ques.cateName }}</span>     
                    </div>
                    <div style="display: flex;">
                        <div class="question-number">{{ writeState.current + 1 }}.</div>
                        <div class="question-content" v-html="filterContent(allTest[writeState.current].ques.content)" />
                    </div>
                    
                    <div class="question-options" v-if="allTest[writeState.current].ques.options && allTest[writeState.current].ques.options.length > 0">
                        <div v-for="(option, optIndex) in allTest[writeState.current].ques.options" :key="optIndex" class="option-item">
                            <span class="option-label">{{ String.fromCharCode(65 + optIndex) }}.</span>
                            <div class="option-content" v-html="option" />
                </div>
                        </div>

                                <div v-show="!writeState.showCorrect">
                                        <div class="show-analyse">
                                            <el-switch size="small"  @change="togAnswer(allTest[writeState.current],allTest[writeState.current].showAnalyse)"  v-model="allTest[writeState.current].showAnalyse" /> <span>显示答案与解析</span>
                                        </div>
                                        <div v-show="allTest[writeState.current].showAnalyse" class="analyse">
                                            <div class="flex-sty">
                                                <span>【知识点】</span>&nbsp;&nbsp;
                                                <div v-html="allTest[writeState.current].ques.pointVos[0]?.name" />
                                            </div>
                                            <div class="flex-sty">
                                                <span>【答案】</span>&nbsp;&nbsp;
                                                <div v-html="allTest[writeState.current].ques.displayAnswer" />
                                            </div>
                                            <div class="flex-sty">
                                                <span>【分析】</span>&nbsp;&nbsp;
                                                <div v-html="allTest[writeState.current].ques.analyse" />
                                            </div>
                                            <div class="flex-sty">
                                                <span>【解答】</span>&nbsp;&nbsp;
                                                <div v-html="allTest[writeState.current].ques.method" />
                                            </div>
                                            <div class="flex-sty">
                                                <span>【点评】</span>&nbsp;&nbsp;
                                                <div v-html="allTest[writeState.current].ques.discuss" />
                                            </div>
                                        </div>
                                    </div>


                </div>
                <!-- 答题区域 -->
                <div class="answer-section">
                                <!-- 选择题答题区域 -->
                    <div v-if="allTest[writeState.current].ques.cate == 1 || allTest[writeState.current].ques.cate == 3" class="choice-answer-area">
                                    <!-- 选项区域 -->
                        <div class="answer-options">
                            <div 
                                v-for="(option, optIndex) in allTest[writeState.current].ques.options" 
                                :key="optIndex" 
                                class="answer-option"
                                :class="{ 
                                    'selected': allTest[writeState.current].ques.userJson && allTest[writeState.current].ques.userJson.includes(optIndex),
                                    'disabled': allTest[writeState.current].submitted === true || allTest[writeState.current].userMark !== null
                                }"
                                            @click="handleOptionClick(optIndex)"
                            >
                                <div class="option-checkbox">
                                    <i v-if="allTest[writeState.current].ques.userJson && allTest[writeState.current].ques.userJson.includes(optIndex)" class="checkmark">✓</i>
                        </div>
                                <span class="option-letter">{{ String.fromCharCode(65 + optIndex) }}</span>
                    </div>
                        </div>
                    </div>
                    
                                <!-- 非选择题答题区域 -->
                    <div v-else class="non-choice-answer-area">
                        <!-- 🎯 优化：主观题批改模式 -->
                        <div v-if="writeState.showSubjective" class="subjective-correction-container">
                            <!-- 题目信息区域 -->
                            <div class="question-info-section">
                                <div class="question-header">
                                    <span class="question-type">{{ allTest[writeState.current].ques.cateName }}</span>
                                    <span class="question-number">第{{ writeState.current + 1 }}题</span>
                                </div>
                                
                                <!-- 显示答案与解析开关 -->
                                <div class="show-analyse">
                                    <el-switch 
                                        @change="togAnswer(allTest[writeState.current], allTest[writeState.current].showAnalyse)" 
                                        size="small" 
                                        v-model="allTest[writeState.current].showAnalyse" 
                                    /> 
                                    <span>显示答案与解析</span>
                                </div>
                                
                                <!-- 答案解析区域 -->
                                <div v-show="allTest[writeState.current].showAnalyse" class="analyse">
                                    <div class="flex-sty">
                                        <span>【知识点】</span>&nbsp;&nbsp;
                                        <div v-if="allTest[writeState.current].ques.pointVos != null" v-html="allTest[writeState.current].ques.pointVos[0]?.name" />
                                        <div v-else>--</div>
                                    </div>
                                    <div class="flex-sty">
                                        <span>【答案】</span>&nbsp;&nbsp;
                                        <div v-html="allTest[writeState.current].ques.displayAnswer" />
                                    </div>
                                    <div class="flex-sty">
                                        <span>【分析】</span>&nbsp;&nbsp;
                                        <div v-html="allTest[writeState.current].ques.analyse" />
                                    </div>
                                    <div class="flex-sty">
                                        <span>【解答】</span>&nbsp;&nbsp;
                                        <div v-html="allTest[writeState.current].ques.method" />
                                    </div>
                                    <div class="flex-sty">
                                        <span>【点评】</span>&nbsp;&nbsp;
                                        <div v-html="allTest[writeState.current].ques.discuss" />
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 用户答案图片区域 -->
                            <div class="user-answer-section">
                                <div class="section-title">
                                    <span>学生答案</span>
                                    <span class="image-count">({{ allTest[writeState.current].userAnswer ? allTest[writeState.current].userAnswer.length : 0 }}张图片)</span>
                                </div>
                                
                                <div class="answer-img-box" v-if="allTest[writeState.current].userAnswer && allTest[writeState.current].userAnswer.length > 0">
                                    <el-image
                                        class="answer-img"
                                        v-for="(it, ind) in allTest[writeState.current].userAnswer"
                                        :key="ind"
                                        style="width: 10.8125rem; height: 10.8125rem; border-radius: .25rem; margin: 5px;"
                                        :src="it"
                                        :zoom-rate="1.2"
                                        :max-scale="7"
                                        :min-scale="0.2"
                                        :preview-src-list="allTest[writeState.current].userAnswer"
                                        show-progress
                                        :initial-index="ind"
                                        fit="cover"
                                    />
                                </div>
                                
                                <div v-else class="no-answer">
                                    <span>暂无学生答案</span>
                                </div>
                            </div>
                            
                            <!-- 批改按钮区域 -->
                            <div class="correction-section">
                                <div class="section-title">
                                    <span>批改结果</span>
                                    <span v-if="allTest[writeState.current].userMark !== null" class="current-result">
                                        (当前: {{ allTest[writeState.current].userMark === 1 ? '正确' : allTest[writeState.current].userMark === 2 ? '半对' : '错误' }})
                                    </span>
                                </div>
                                
                                <div class="answers">
                                    <div 
                                        class="answer-box" 
                                        @click="correcthandle(writeState.current, 1)" 
                                        :class="allTest[writeState.current].userMark == 1 ? 'green-box selected' : ''"
                                    >
                                        <div></div>正确
                                    </div>
                                    <div 
                                        class="answer-box" 
                                        @click="correcthandle(writeState.current, 2)" 
                                        :class="allTest[writeState.current].userMark == 2 ? 'yellow-box selected' : ''"
                                    >
                                        <div></div>半对
                                    </div>
                                    <div 
                                        class="answer-box" 
                                        @click="correcthandle(writeState.current, 0)" 
                                        :class="allTest[writeState.current].userMark == 0 ? 'red-box selected' : ''"
                                    >
                                        <div></div>错误
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 🎯 优化：正常答题模式 -->
                        <div v-else class="normal-answer-mode">
                            <div class="paper-content-ques">
                                <!-- 已上传的图片显示 -->
                                <div v-show="!writeState.showCorrect" class="upload-images">
                                    <div 
                                        v-for="(it, ind) in allTest[writeState.current].userAnswer"
                                        :key="ind"
                                        class="upload-image-item"
                                    >
                                        <el-image
                                            class="answer-img"
                                            :src="it"
                                            :zoom-rate="1.2"
                                            :max-scale="7"
                                            :min-scale="0.2"
                                            :preview-src-list="allTest[writeState.current].userAnswer"
                                            show-progress
                                            :initial-index="ind"
                                            fit="cover"
                                        />
                                    </div>
                                </div>
                                <!-- 上传组件 -->
                                <uploadAnswerImg v-if="writeState.showCorrect" :imgList="allTest[writeState.current].ques.userJson" :index="writeState.current" @getImgList="handleImgList" />
                            </div>
                        </div>
                    </div>

                <!-- 底部区域保留空间 -->
                <div class="question-footer">
                    <div class="navigation-buttons">
                        <!-- 导航按钮已移至作答区域，这里可以放置其他内容 -->
                        </div>
                                    </div>
            </div>
        </div>
      </div>
      <div class="right">
        <div class="time-box">
            <div class="time-text" > <img src="@/assets/img/percision/training/times.png" alt="">已用时间</div>
            <div style="display: flex; " class="time-bg">
                <div class="time-number"> {{ timeState.hours < 10 ? "0" + timeState.hours : timeState.hours }} </div> :
                <div class="time-number"> {{ timeState.minutes < 10 ? "0" + timeState.minutes : timeState.minutes }} </div> :
                <div class="time-number"> {{ timeState.seconds < 10 ? "0" + timeState.seconds : timeState.seconds }} </div>
            </div>    
        </div>
        <div class="test-number-box">
                        <div class="question-nav-title"><img src="@/assets/img/percision/training/tihao.png" alt=""> 题号 ( {{ allTest.length }} 题)</div>
            <div class="question-nav-grid">
                <div 
                    v-for="(item, index) in allTest"
                    :key="index"
                    class="test-number-item" 
                    :class="[
                        setClass1(item, index)
                    ]"
                    @click="switchQuestion(index)"
                > 
                    {{ index + 1 }} 
                </div>
            </div>
            <div class="submit-section">
                <!-- 🎯 优化：智能按钮显示逻辑 -->
                
                <!-- 🎯 优化：下一题按钮：当还有未作答题目时显示 -->
                <button 
                    v-if="shouldShowNextButton" 
                    class="next-question-btn"
                    :class="{ 'disabled': writeState.current === null || !allTest[writeState.current] || !checkIfHasAnswer(allTest[writeState.current]) }"
                    @click="goToNextQuestion()"
                >
                    <img 
                        src="@/assets/img/percision/training/ydt.png"
                        style="width: 20px;height: 20px;margin-right: 5px;" 
                        alt=""
                    >
                    下一题
                </button>
                
                <!-- 🎯 优化：完成按钮：当所有题目都已作答且在最后一题时显示 -->
                <button 
                    v-if="shouldShowFinishButton" 
                    class="finish-btn"
                    @click="submit"
                >
                    <img 
                        src="@/assets/img/percision/training/ydt.png" 
                        style="width: 20px;height: 20px;margin-right: 5px;" 
                        alt=""
                    >
                    完成
                </button>
                
                <!-- 🎯 新增：当所有题目都已作答时的提示 -->
                <div 
                    v-if="writeState.current !== null && allQuestionsAnswered" 
                    class="all-answered-tip"
                    style="font-size: 14px; color: #10b981; margin-top: 10px; text-align: center;"
                >
                    ✅ 所有题目已完成，可以点击完成按钮提交
                </div>
                
                <!-- 🎯 新增：最后一题但还有未作答题目的提示 -->
                <div 
                    v-if="writeState.current !== null && writeState.current === allTest.length - 1 && !allQuestionsAnswered" 
                    class="last-question-tip"
                    style="font-size: 14px; color: #f56565; margin-top: 10px; text-align: center;"
                >
                    ⚠️ 还有题目未作答，请先完成所有题目
                </div>
                
                <!-- 🎯 新增：调试信息显示 -->
                <div v-if="writeState.current !== null && allTest[writeState.current]" class="debug-info" style="font-size: 12px; color: #666; margin-top: 5px;">
                    当前题目: {{ writeState.current + 1 }}/{{ allTest.length }} | 
                    所有题目已作答: {{ allQuestionsAnswered ? '是' : '否' }} | 
                    当前题目已作答: {{ checkIfHasAnswer(allTest[writeState.current]) ? '是' : '否' }}
                </div>
                
            <!-- <div class="icon-btn size285" :class="writeState.disabled?'disabled':''" @click="submit" v-loading="writeState.btnloading">
                <img src="@/assets/img/percision/submit.png" alt="">
                提交批改
                </div> -->
            </div>
             <div class="color-type">
                 <div class="legend-item">
                     <div class="legend-square undone"></div>
                     <span>未做</span>
                 </div>
                 <div class="legend-item">
                     <div class="legend-square done"></div>
                     <span>已做</span>
                 </div>
                 <div class="legend-item">
                     <div class="legend-square correct"></div>
                     <span>正确</span>
                 </div>
                 <div class="legend-item">
                     <div class="legend-square half-correct"></div>
                     <span>半对</span>
                 </div>
                 <div class="legend-item">
                     <div class="legend-square wrong"></div>
                     <span>错误</span>
                 </div>
              </div>
            </div>
        </div>
      </div>
      </div>
    </div>
    <el-dialog class="dialog-correct" v-model="writeState.showDialog" title="提交" align-center center>
        <p class="black-text">{{ writeState.subjectiveNum }}道主观题需要对照答案批改</p>
        <p class="grey-text">（同学们可以邀请家长一起完成哦）</p>
        <template #footer>
        <div class="dialog-footer">
            <div class="blue-btn" @click="toCorrect">去批改</div>
        </div>
        </template>
    </el-dialog>
    <!-- <coinAlert :show="writeState.jfShow" :num="writeState.jfNum" :isAjax="false" @close="jfHide">
    </coinAlert> -->
</template>

<script lang="ts" setup>
import { watch, onMounted, reactive, ref, onUnmounted, nextTick, computed } from 'vue'
import uploadAnswerImg from '@/views/components/uploadAnswerImg/indexNew.vue'
import coinAlert from "@/views/components/coinAlert/index.vue"
import { dataDecrypt, dataEncrypt, mergeObject } from "@/utils/secret"
import { useRouter, useRoute } from 'vue-router'
import fiveStep from "@/views/components/fiveStep/index.vue"
import { quesGetApi} from "@/api/video"
import { Action, ElMessage, ElMessageBox, ElIcon, ElButton } from 'element-plus'
import { Loading, Document, SuccessFilled, CircleCheckFilled } from '@element-plus/icons-vue'
import { createTrainToAtlasApi, getDetailsApi, saveToAtlasApi, checkTrainQuesApi, getDetailssApi, saveUserJsonApi,saveToIntroduceddApi } from '@/api/training'
import {  savePointTrainingApi,saveTrainingApi} from "@/api/precise"
import { fa } from 'element-plus/es/locale'
const route = useRoute()
const router = useRouter()
const timeState = reactive({
    hours: 0,
    minutes: 0,
    seconds: 0
})
const writeState:any = reactive({
    current: 0 as number | null,
    step: 1,
    btnloading: false,
    showCorrect: true,
    loading: false,
    showStep: false,
    disabled: true,
    trainingId: "",
    //积分
    jfShow: false,
    jfHide: true,
    jfNum: '0',
    jfSource: '0',
    itemTimer: 0, // 单题计时器
    lastTimestamp: 0, // 上次计时时间戳
    fromNextButton: false, // 标记是否从"下一题"按钮触发提交
    expectedNextIndex: undefined, // 期望的下一题索引
    showDialog:false,
    subjectiveNum: 0, // 主观题数量
    unWriteNum: [] as number[]
})
interface Ques {
    cate: number;
    cateName: string;
    content: string;
    displayAnswer: string;
    analyse: string;
    method: string;
    discuss: string;
    options: any[];
    pointVos: any[];
    userJson: any[];
    answers: any[];
}

class AData {
    quesId: string = "";
    cate: number = 0;
    cateName: string = "";
    trainTime: string = "";
    userAnswer: string[] = [];
    userMark: number | null = null;
    userMarks: number | null = null;
    showAnalyse: boolean = false;
    content: string = "";
    ques: Ques = { // 添加 ques 属性
        cate: 0,
        cateName: "",
        content: "",
        analyse: "",
        discuss: "",
        method: "",
        displayAnswer: "",
        options: [],
        pointVos: [],
        userJson: [],
        answers: []
    };
}
let detailData
let timer :  NodeJS.Timeout | null = null
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
const allTest = ref([] as any[])
watch(() => timeState.seconds, () => {
    if(timeState.seconds == 60) {
        timeState.minutes ++
        timeState.seconds = 0
    }
    if(timeState.minutes == 60) {
        timeState.hours ++
        timeState.minutes = 0
    }

})

const goBack = () =>{
    router.go(-1)
}

// 🎯 优化：计算答题进度百分比，基于已提交题目数量
const progressPercentage = computed(() => {
    if (!allTest.value || allTest.value.length === 0) {
        return 0
    }
    
    // 🎯 修改：只计算已提交（saveUserJsonApi成功）的题目数量
    const submittedCount = allTest.value.filter(item => {
        // 检查是否已提交（submitted为true或userMark不为null表示已通过saveUserJsonApi）
        return item.submitted === true || item.userMark !== null
    }).length
    
    const totalCount = allTest.value.length
    let percentage = Math.round((submittedCount / totalCount) * 100)
    
    // 确保进度在0-100之间
    percentage = Math.max(0, Math.min(100, percentage))
    
    // 如果有题目但都未提交，至少显示2%的进度（让图标可见）
    if (totalCount > 0 && percentage === 0) {
        percentage = 2
    }
    
    console.log('📊 答题进度计算（基于已提交）:', {
        已提交题数: submittedCount,
        总题数: totalCount,
        进度百分比: percentage + '%',
        详细状态: allTest.value.map((item, index) => ({
            题目: index + 1,
            已提交: item.submitted,
            已批改: item.userMark !== null
        }))
    })
    
    return percentage
})

// 🎯 优化：检查所有题目是否都已作答，使用统一的检查逻辑
const allQuestionsAnswered = computed(() => {
    if (!allTest.value || allTest.value.length === 0) {
        return false
    }
    
    // 🎯 优化：使用统一的checkIfHasAnswer函数检查所有题目
    const allAnswered = allTest.value.every(item => checkIfHasAnswer(item))
    
    console.log('🔍 检查所有题目是否都已作答:', {
        总题数: allTest.value.length,
        所有题目已作答: allAnswered,
        详细状态: allTest.value.map((item, index) => ({
            题目: index + 1,
            题目类型: item.ques?.cate === 1 ? '单选题' : item.ques?.cate === 3 ? '多选题' : '非选择题',
            选择题答案: item.ques?.userJson,
            非选择题答案: item.userAnswer,
            已作答: checkIfHasAnswer(item),
            检查结果: checkIfHasAnswer(item) ? '✅ 已作答' : '❌ 未作答'
        }))
    })
    
    return allAnswered
})

// 🎯 新增：检查是否还有未作答的题目
const hasUnansweredQuestions = computed(() => {
    if (!allTest.value || allTest.value.length === 0) {
        return false
    }
    
    return allTest.value.some(item => !checkIfHasAnswer(item))
})

// 🎯 优化：检查是否应该显示完成按钮
const shouldShowFinishButton = computed(() => {
    if (writeState.current === null || allTest.value.length === 0) {
        return false
    }
    
    const isLastQuestion = writeState.current === allTest.value.length - 1
    const allAnswered = allQuestionsAnswered.value
    
    // 🎯 优化：当所有题目都已作答时，无论在哪个题目都显示完成按钮
    const shouldShow = allAnswered
    
    console.log('🎯 完成按钮显示检查:', {
        当前题目索引: writeState.current,
        总题目数: allTest.value.length,
        是否最后一题: isLastQuestion,
        所有题目已作答: allAnswered,
        应该显示完成按钮: shouldShow,
        说明: shouldShow ? '✅ 所有题目已作答，显示完成按钮' : '❌ 还有未作答题目'
    })
    
    return shouldShow
})

// 🎯 优化：检查是否应该显示下一题按钮
const shouldShowNextButton = computed(() => {
    if (writeState.current === null || allTest.value.length === 0) {
        return false
    }
    
    const isLastQuestion = writeState.current === allTest.value.length - 1
    const allAnswered = allQuestionsAnswered.value
    const hasUnanswered = hasUnansweredQuestions.value
    
    // 🎯 优化：如果所有题目都已作答，不显示下一题按钮
    if (allAnswered) {
        console.log('🎯 下一题按钮显示检查: 所有题目已作答，不显示下一题按钮')
        return false
    }
    
    // 如果还有未作答题目，显示下一题按钮
    const shouldShow = hasUnanswered
    
    console.log('🎯 下一题按钮显示检查:', {
        当前题目索引: writeState.current,
        总题目数: allTest.value.length,
        是否最后一题: isLastQuestion,
        所有题目已作答: allAnswered,
        还有未作答题目: hasUnanswered,
        应该显示下一题按钮: shouldShow
    })
    
    return shouldShow
})

// 自定义返回方法
const customGoBack = () => {
    router.go(-1)
}
onMounted(async () => {
    console.log(queryData,"queryDataqueryDataqueryData111111426666")
    // 初始化时间戳
    writeState.lastTimestamp = Date.now()
    
    try {
        // 优化：先设置加载状态，避免页面空白
        writeState.loading = true
        
        // 调用获取详情接口
        await getDetails()
        
    } catch (error) {
        console.error('页面初始化失败:', error)
        ElMessage.error('页面加载失败，请刷新重试')
        writeState.loading = false
    }

    // 设置自定义返回方法
    window.customGoBack = customGoBack
})

onUnmounted(() => {
    if (timer !== null) { // 添加类型安全检查
        clearInterval(timer)
        timer = null // 确保timer被清空
    }
    // 重置计时器状态
    writeState.itemTimer = 0

    // 清除自定义返回方法
    if (window.customGoBack) {
        delete window.customGoBack
    }
})
// 隐藏积分
const jfHide = () => {
    writeState.jfShow = false
}
// 获取学习步骤
const sendStep = ( data: number) => {
    writeState.step = data
}

// 将时分秒转换为毫秒时间戳
const convertTimeToMilliseconds = () => {
    const totalSeconds = timeState.hours * 3600 + timeState.minutes * 60 + timeState.seconds
    return totalSeconds * 1000 // 转换为毫秒
}

  //显示答案
const togAnswer = async (item:any,isShow:any) => {
    if(isShow){
        // 如果已经有完整的题目信息，直接显示，无需重复请求
        if (item.ques.analyse && item.ques.method && item.ques.discuss) {
            return
        }
        try {
            // 添加加载状态，防止重复点击
            if (item.loading) return
            item.loading = true

            const response = await quesGetApi({id: item.ques.quesId}) as any

            if (response.code === 200 && response.data) {
                // 使用 Object.assign 来安全地合并数据，保留原有属性
                Object.assign(item.ques, response.data)

                // 确保必要的属性存在
                if (!item.ques.pointVos) {
                    item.ques.pointVos = []
                }
                if (!item.ques.options) {
                    item.ques.options = []
                }
                if (!item.ques.answers) {
                    item.ques.answers = []
                }
            } else {
                console.error('获取题目详细信息失败:', response)
                // 如果获取失败，关闭显示开关
                item.showAnalyse = false
                // 可以添加用户提示
            }
        } catch (error) {
            console.error('获取题目详细信息时发生错误:', error)
            // 发生错误时关闭显示开关
            item.showAnalyse = false
            // 可以添加用户提示
        } finally {
            // 清除加载状态
            item.loading = false
        }
    }

}

const createTrain = () => {
    const formdata = new FormData()
    formdata.append("sourceId", queryData.sourceId)
    formdata.append("noteSource", '1')
    // for(let i of queryData.reportId){
    //   formdata.append("pointIds[]", i)
    // }
    formdata.append("step", queryData.step)
    createTrainToAtlasApi(formdata).then((res: any) => {
        if (res.data) {
            writeState.trainingId = res.data
            writeState.showStep = true

            // 获取详情
            getDetails()

        }

    }).catch((error) => {
    })
}
const getDetails = async () => {
    try {
        // 🎯 优化：直接使用动态trainingId，移除缓存逻辑
        const trainingId = queryData.reportId || queryData.trainingId || '1970022780401995777'
               
        // 直接调用API获取数据
        const res1 = await getDetailssApi({trainingId}) as any
        
        if (res1.code === 200) {
            console.log('✅ 获取训练详情成功')
            
            // 直接处理返回的数据
            processDetailsData(res1)
            
        } else {
            console.error('❌ 获取训练详情失败:', res1)
            ElMessage.error(res1.message || '获取训练详情失败')
            writeState.loading = false
        }

    } catch (error) {
        console.error('❌ 获取训练详情时发生错误:', error)
        ElMessage.error('网络错误，请检查网络连接后重试')
        writeState.loading = false
    }
}

// 提取数据处理逻辑为独立方法
const processDetailsData = (res1: any) => {
    try {
        console.log('🔄 开始处理训练详情数据')
        
        // 验证数据完整性
        if (!res1.data || !res1.data.items || !Array.isArray(res1.data.items)) {
            throw new Error('训练数据格式不正确')
        }
        
    detailData = res1.data
        
        // 设置训练时间
        if (res1.data.trainTime) {
    timeState.seconds = Number(res1.data.trainTime) / 1000
            // console.log('⏰ 设置训练时间:', timeState.seconds, '秒')
        }
        
        // 处理题目数据
        res1.data.items.forEach((item, index) => {
            // 🔍 调试：打印每个题目的原始数据
            // console.log(`🔍 题目${index + 1}原始数据检查:`, {
            //     userAnswer: item.userAnswer,
            //     userMark: item.userMark,
            //     submitted: item.submitted,
            //     cate: item.ques?.cate,
            //     cateName: item.ques?.cateName
            // })
            
            // 初始化基础属性
        item.showAnalyse = false
        
            // 确保ques对象存在
            if (!item.ques) {
                console.warn(`⚠️ 题目${index + 1}缺少ques对象`)
                item.ques = {
                    cate: 0,
                    cateName: "",
                    content: "",
                    analyse: "",
                    discuss: "",
                    method: "",
                    displayAnswer: "",
                    options: [],
                    pointVos: [],
                    userJson: [],
                    answers: []
                }
            }
            
            // 🎯 修复：优先检查userAnswer是否有值，恢复已答题目的数据
            const hasUserAnswer = item.userAnswer && item.userAnswer.length > 0
            const isChoiceQuestion = item.ques.cate === 1 || item.ques.cate === 3
            const isSubmittedOrGraded = item.userMark !== null || item.submitted === true
            
            if (hasUserAnswer && isChoiceQuestion) {
                // 选择题且有用户答案：恢复答案数据
                try {
                    // 处理不同格式的userAnswer
                    let userAnswerArray = []
                    if (Array.isArray(item.userAnswer)) {
                        userAnswerArray = item.userAnswer
                    } else if (typeof item.userAnswer === 'string') {
                        // 可能是JSON字符串或逗号分隔的字符串
                        try {
                            userAnswerArray = JSON.parse(item.userAnswer)
                        } catch {
                            userAnswerArray = item.userAnswer.split(',').filter(v => v.trim())
                        }
                    }
                    
                    item.ques.userJson = userAnswerArray.map((answer: any) => parseInt(answer))
                    // console.log(`✅ 恢复题目${index + 1}选择题答案:`, {
                    //     题目类型: item.ques.cateName,
                    //     原始userAnswer: item.userAnswer,
                    //     处理后数组: userAnswerArray,
                    //     最终userJson: item.ques.userJson,
                    //     是否已提交: isSubmittedOrGraded
                    // })
                } catch (error) {
                    console.error(`❌ 题目${index + 1}答案处理失败:`, error)
                    item.ques.userJson = []
                }
            } else if (hasUserAnswer && !isChoiceQuestion) {
                // 🎯 优化：非选择题且有用户答案，确保数据正确恢复
                item.ques.userJson = []
                
                // 验证userAnswer是否为有效的图片URL数组
                const validImages = item.userAnswer.filter(url => 
                    typeof url === 'string' && url.trim().length > 0
                )
                
                if (validImages.length !== item.userAnswer.length) {
                    console.warn(`⚠️ 题目${index + 1}存在无效图片URL:`, {
                        原始数组: item.userAnswer,
                        有效图片: validImages
                    })
                    // 清理无效数据
                    item.userAnswer = validImages
                }
                
                // console.log(`✅ 题目${index + 1}非选择题数据恢复:`, {
                //     题目类型: item.ques.cateName,
                //     图片数量: item.userAnswer.length,
                //     图片URLs: item.userAnswer,
                //     是否已提交: item.submitted,
                //     是否已批改: item.userMark !== null,
                //     批改结果: item.userMark,
                //     完整状态: isSubmittedOrGraded ? '已提交/批改' : '未提交'
                // })
        } else {
                // 没有用户答案：初始化为空
            item.ques.userJson = []
                // console.log(`📝 题目${index + 1}无答案，初始化userJson`, {
                //     题目类型: item.ques.cateName,
                //     hasUserAnswer,
                //     isChoiceQuestion
                // })
            }
        })
        
        // 设置题目数据
        allTest.value = res1.data.items
        console.log('📚 题目数据已设置，共', allTest.value.length, '题')

        // 检查是否有未完成的题目并设置当前题目
        findAndSetCurrentQuestion()
        
        // 启动计时器（如果有未完成题目）
        startTimerIfNeeded()
        
        writeState.loading = false
        console.log('✅ 训练详情数据处理完成')
        
        // 🎯 验证非选择题数据恢复状态
        validateNonChoiceQuestionsData()
        
        // 🎯 调试：打印题号状态信息
        setTimeout(() => {
            debugQuestionStatus()
        }, 100)
        
    } catch (error) {
        console.error('❌ 处理训练详情数据时发生错误:', error)
        // ElMessage.error('数据处理失败：' + error.message)
        writeState.loading = false
    }
}

// 🎯 优化：查找并设置当前题目，优先显示未作答的题目
const findAndSetCurrentQuestion = () => {
    let hasUnansweredQuestions = false
    let currentQuestionSet = false
    
    // 🎯 优化：优先查找未作答的题目
    for (let i = 0; i < allTest.value.length; i++) {
        const item = allTest.value[i]
        if (!checkIfHasAnswer(item)) {
            writeState.current = i
            writeState.itemTimer = 0 // 重置单题计时器
            hasUnansweredQuestions = true
            currentQuestionSet = true
            console.log('📍 设置当前题目为未作答题目:', i + 1)
            break
        }
    }
    
    // 如果没有未作答的题目，但有题目，则设置为第一题（用于查看模式）
    if (!currentQuestionSet && allTest.value.length > 0) {
        writeState.current = 0
        console.log('📍 设置当前题目为第一题（查看模式）:', 1)
    }
    
    // 检查是否所有题目都已作答
    if (!hasUnansweredQuestions && allTest.value.length > 0) {
        const allAnswered = allQuestionsAnswered.value
        if (allAnswered) {
            // 🎯 优化：所有题目完成后不设置为null，保持当前题目索引以继续显示答题内容
            // writeState.current = null  // 注释掉这行，保持当前题目索引
            writeState.disabled = false
            console.log('🎉 所有题目都已作答，保持当前题目显示')
        }
    }
    
    // 🎯 设置初始答题区域显示模式
    if (writeState.current !== null) {
        updateAnswerAreaDisplay(writeState.current)
    }
    
    return hasUnansweredQuestions
}

// 新增：根据需要启动计时器
const startTimerIfNeeded = () => {
    // 🎯 优化：即使所有题目都已完成，也保持计时器运行（因为用户可能还在查看题目）
    // 只有当真正没有题目时才停止计时器
    if (allTest.value.length === 0) {
        if (timer !== null) {
            clearInterval(timer)
            timer = null
        }
        return
    }
    
    // 如果计时器还未启动，启动它
    if (timer === null) {
        timer = setInterval(() => {
            timeState.seconds++
            writeState.itemTimer++ // 更新单题计时
        }, 1000)
        console.log('⏱️ 计时器已启动')
    }
}
const setClass = (item: any, index: number) => {
    let classState = ""
    if (item.userMark != null) {
        if (item.userMark == 0) {
            classState = "red-border"
        } else if (item.userMark == 1) {
            classState = "green-border"
        } else if (item.userMark == 2) {
            classState = "yellow-border"
        }

    } else if (writeState.current == index) {
        classState = "black-text"
    }
    return classState
}

// 🎯 优化：去批改主观题
const toCorrect = () => {
    console.log('📝 开始批改主观题流程')
    
    // 🎯 优化：获取所有非选择题（主观题）
    const subjectiveQuestions = allTest.value.filter(item => item.ques.cate != 1 && item.ques.cate != 3)
    
    console.log('🔍 发现的主观题:', subjectiveQuestions.map((item, index) => ({
        题目: allTest.value.indexOf(item) + 1,
        类型: item.ques.cateName,
        题目分类: item.ques.cate,
        用户答案: item.userJson,
        批改状态: item.userMark
    })))
    
    // 🎯 优化：处理非选择题的图片数据
    subjectiveQuestions.forEach((item: any) => {
        console.log(`🔄 处理题目${allTest.value.indexOf(item) + 1}的图片数据:`, {
            原始userJson: item.userJson,
            题目类型: item.ques.cateName
        })
        
        if (item.userJson && Array.isArray(item.userJson)) {
            // 如果userJson是对象数组，提取url
            if (item.userJson.length > 0 && typeof item.userJson[0] === 'object' && item.userJson[0].url) {
                item.userJson = item.userJson.map((img: any) => img.url)
                console.log(`✅ 题目${allTest.value.indexOf(item) + 1}图片数据已转换:`, item.userJson)
            }
            // 如果userJson已经是URL数组，保持不变
            else if (item.userJson.length > 0 && typeof item.userJson[0] === 'string') {
                console.log(`✅ 题目${allTest.value.indexOf(item) + 1}图片数据已是URL格式:`, item.userJson)
            }
        }
        
        // 设置images用于显示
        item.images = [...(item.userJson || [])]
    })
    
    // 🎯 优化：激活主观题批改模式
    writeState.showDialog = false
    writeState.showSubjective = true
    writeState.isAllCorrect = false
    
    console.log('✅ 主观题批改环节已激活:', {
        showSubjective: writeState.showSubjective,
        isAllCorrect: writeState.isAllCorrect,
        主观题数量: subjectiveQuestions.length,
        批改状态: subjectiveQuestions.map(item => ({
            题目: allTest.value.indexOf(item) + 1,
            已批改: item.userMark !== null,
            批改结果: item.userMark === 1 ? '正确' : item.userMark === 2 ? '半对' : item.userMark === 0 ? '错误' : '未批改'
        }))
    })
    
    // 🎯 新增：强制触发响应式更新
    setTimeout(() => {
        console.log('🔄 强制触发响应式更新，确保非选择题正确渲染')
        // 这里可以添加一些强制更新的逻辑
    }, 100)
}

// 🎯 新增：批改主观题
const correcthandle = (index: number, userMark: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`)
        return
    }

    // 确保 allTest.value[index] 存在
    const currentItem = allTest.value[index]
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`)
        return
    }

    // 🎯 优化：更新批改结果
    currentItem.userMark = userMark
    
    console.log(`📝 批改题目${index + 1}:`, {
        题目类型: currentItem.ques.cateName,
        批改结果: userMark === 1 ? '正确' : userMark === 2 ? '半对' : '错误',
        用户答案: currentItem.userAnswer
    })
    
    // 🎯 优化：判断是否已全部批改
    writeState.isAllCorrect = true
    const subjectiveQuestions = allTest.value.filter(item => item.ques.cate != 1 && item.ques.cate != 3)
    
    subjectiveQuestions.forEach((item: any) => {
        if (item.userMark == null) {
            writeState.isAllCorrect = false
        }
    })
    
    console.log('📊 批改进度:', {
        总主观题数: subjectiveQuestions.length,
        已批改数: subjectiveQuestions.filter(item => item.userMark !== null).length,
        未批改数: subjectiveQuestions.filter(item => item.userMark === null).length,
        是否全部批改: writeState.isAllCorrect
    })
}

// 🎯 新增：继续提交流程（批改主观题后调用）
const continueSubmit = async () => {
    console.log('🔄 继续提交流程')
    writeState.btnloading = true
    
    // 继续执行原来的提交流程
    await executeSubmit()
}

// 题号颜色
const setClass1 = (item: any, index: number) => {
    let classState = ""
    
    // 🎯 优化：只有已批改的题目才显示结果颜色
    if (item.userMark !== null && item.userMark !== undefined) {
        // 已批改：显示批改结果颜色
        if (item.userMark === 0) {
            classState = "red"      // 错误
        } else if (item.userMark === 1) {
            classState = "green"    // 正确
        } else if (item.userMark === 2) {
            classState = "yellow"   // 半对
        }
        // console.log(`题目${index + 1}已批改，显示结果颜色:`, classState)
    } else {
        // 未批改的题目：根据答题状态显示
        const hasAnswer = checkIfHasAnswer(item)
        
        if (hasAnswer) {
            // 已作答但未批改：显示蓝色
        classState = "blue"
            // console.log(`题目${index + 1}已作答未批改，显示蓝色`)
        } else if (writeState.current === index) {
            // 当前题目：显示蓝色高亮
            classState = "blue"
            // console.log(`题目${index + 1}当前题目，显示蓝色高亮`)
        } else {
            // 未作答：显示默认状态（白色）
            classState = ""
            // console.log(`题目${index + 1}未作答，显示默认状态`)
        }
    }
    
    return classState
}

// 🎯 优化：检查题目是否已作答的辅助方法，支持所有题目类型
const checkIfHasAnswer = (item: any) => {
    // 🎯 修复：添加空值检查，防止 undefined 错误
    if (!item || typeof item !== 'object') {
        console.warn('⚠️ checkIfHasAnswer: item 参数无效:', item);
        return false;
    }
    
    // 确保 ques 对象存在
    if (!item.ques || typeof item.ques !== 'object') {
        console.warn('⚠️ checkIfHasAnswer: item.ques 不存在:', item);
        return false;
    }
    
    const isChoiceQuestion = item.ques.cate === 1 || item.ques.cate === 3
    
    if (isChoiceQuestion) {
        // 选择题：检查userJson是否有选择
        const hasChoiceAnswer = item.ques.userJson && Array.isArray(item.ques.userJson) && item.ques.userJson.length > 0
        // console.log(`🔍 选择题${item.ques.cateName}作答检查:`, {
        //     userJson: item.ques.userJson,
        //     hasChoiceAnswer,
        //     题目类型: item.ques.cate === 1 ? '单选题' : '多选题'
        // });
        return hasChoiceAnswer
    } else {
        // 🎯 优化：非选择题：检查userAnswer是否有上传的图片或文件
        const hasUploadAnswer = item.userAnswer && Array.isArray(item.userAnswer) && item.userAnswer.length > 0
        
        // 额外检查：如果userAnswer为空但userJson有值，也认为已作答
        const hasUserJsonAnswer = item.ques.userJson && Array.isArray(item.ques.userJson) && item.ques.userJson.length > 0
        
        const finalResult = hasUploadAnswer || hasUserJsonAnswer;
        
        console.log(`🔍 非选择题${item.ques.cateName}作答检查:`, {
            userAnswer: item.userAnswer,
            userJson: item.ques.userJson,
            hasUploadAnswer,
            hasUserJsonAnswer,
            finalResult,
            题目类型: '非选择题'
        });
        
        return finalResult
    }
}

// 🎯 新增：验证非选择题数据恢复状态
const validateNonChoiceQuestionsData = () => {
    console.log('=== 验证非选择题数据恢复状态 ===')
    
    const nonChoiceQuestions = allTest.value.filter(item => 
        item.ques?.cate !== 1 && item.ques?.cate !== 3
    )
    
    if (nonChoiceQuestions.length === 0) {
        console.log('📝 当前没有非选择题')
        return
    }
    
    nonChoiceQuestions.forEach((item, index) => {
        const realIndex = allTest.value.findIndex(q => q === item)
        const hasImages = item.userAnswer && Array.isArray(item.userAnswer) && item.userAnswer.length > 0
        const hasAnswer = checkIfHasAnswer(item)
        const colorStatus = setClass1(item, realIndex)
        
        // console.log(`🔍 非选择题${realIndex + 1}验证:`, {
        //     题目类型: item.ques?.cateName,
        //     有图片数据: hasImages,
        //     图片数量: item.userAnswer?.length || 0,
        //     图片URLs: hasImages ? item.userAnswer : '无',
        //     checkIfHasAnswer结果: hasAnswer,
        //     是否已提交: item.submitted,
        //     是否已批改: item.userMark !== null,
        //     题号背景色: colorStatus,
        //     预期显示模式: hasImages ? '查看模式（显示图片）' : '答题模式（显示上传组件）'
        // })
        
        // 验证数据一致性
        if (hasImages !== hasAnswer) {
            console.warn(`⚠️ 题目${realIndex + 1}数据不一致:`, {
                hasImages,
                hasAnswer,
                可能原因: 'checkIfHasAnswer方法判断逻辑需要检查'
            })
        }
    })
    
    console.log('=== 非选择题验证完成 ===')
}

// 🎯 调试方法：验证题号状态显示
const debugQuestionStatus = () => {
    console.log('=== 题号状态调试信息 ===')
    allTest.value.forEach((item, index) => {
        const hasAnswer = checkIfHasAnswer(item)
        const status = setClass1(item, index)
        // console.log(`题目${index + 1}:`, {
        //     题目类型: item.ques?.cateName,
        //     是否已作答: hasAnswer,
        //     是否已批改: item.userMark !== null,
        //     批改结果: item.userMark,
        //     显示状态: status,
        //     userJson: item.ques?.userJson,
        //     userAnswer: item.userAnswer
        // })
    })
    console.log('=== 调试信息结束 ===')
}

// 🎯 优化：点击题号切换题目的方法，支持非选择题查看
const switchQuestion = (index: number) => {
    // console.log('=== 点击题号切换题目 ===')
    // console.log('从题目', writeState.current + 1, '切换到题目', index + 1)
    
    const targetQuestion = allTest.value[index]
    
    const oldQuestion = writeState.current !== null ? allTest.value[writeState.current] : null
    
    if (oldQuestion) {
        console.log('离开题目信息:', {
            题目序号: writeState.current + 1,
            题目类型: oldQuestion?.ques?.cateName,
            用户答案_选择题: oldQuestion?.ques?.userJson,
            用户答案_非选择题: oldQuestion?.userAnswer,
            是否已答题: checkIfHasAnswer(oldQuestion),
            是否已提交: oldQuestion?.submitted === true
        })
    }
    
    // 允许切换到任何题目（包括已答题的题目）
    writeState.itemTimer = 0
    writeState.current = index
    
    // 🎯 优化：根据题目类型和答题状态设置显示模式
    updateAnswerAreaDisplay(index)
    
    console.log('切换到题目信息:', {
        题目序号: index + 1,
        题目类型: targetQuestion?.ques?.cateName,
        题目分类: targetQuestion?.ques?.cate === 1 ? '单选题' : targetQuestion?.ques?.cate === 3 ? '多选题' : '非选择题',
        用户答案_选择题: targetQuestion?.ques?.userJson,
        用户答案_非选择题: targetQuestion?.userAnswer,
        是否已答题: checkIfHasAnswer(targetQuestion),
        是否已提交: targetQuestion?.submitted === true,
        是否已批改: targetQuestion?.userMark !== null,
        当前模式: writeState.showCorrect ? '答题模式' : '查看模式'
    })
    console.log('=== 题目切换完成 ===')
}

// 🎯 优化：统一的答题区域显示逻辑，支持刷新后数据恢复
const updateAnswerAreaDisplay = (questionIndex: number) => {
    const currentQuestion = allTest.value[questionIndex]
    if (!currentQuestion) {
        console.warn('⚠️ 题目不存在:', questionIndex)
        return
    }
    
    const isChoiceQuestion = currentQuestion?.ques?.cate === 1 || currentQuestion?.ques?.cate === 3
    const isSubmittedOrGraded = currentQuestion.submitted === true || currentQuestion.userMark !== null
    const hasUserAnswer = checkIfHasAnswer(currentQuestion)
    
    console.log(`🎯 题目${questionIndex + 1}显示模式判断:`, {
        题目类型: isChoiceQuestion ? '选择题' : '非选择题',
        是否已答: hasUserAnswer,
        是否已提交或批改: isSubmittedOrGraded,
        userAnswer内容: currentQuestion.userAnswer,
        userAnswer长度: currentQuestion.userAnswer?.length || 0,
        userJson长度: currentQuestion.ques?.userJson?.length || 0,
        submitted状态: currentQuestion.submitted,
        userMark状态: currentQuestion.userMark
    })
    
    if (isChoiceQuestion) {
        // 选择题：始终显示选项区域
        writeState.showCorrect = true
        console.log(`✅ 选择题${questionIndex + 1}：显示选项区域`)
    } else {
        // 🎯 优化：非选择题显示逻辑，确保刷新后正确显示已上传图片
        if (hasUserAnswer) {
            if (isSubmittedOrGraded) {
                // 已答题且已提交/批改：查看模式（显示已上传的图片）
        writeState.showCorrect = false
                console.log(`📖 非选择题${questionIndex + 1}：已答题已提交，查看模式 - 显示已上传图片`)
                console.log(`🖼️ 图片列表:`, currentQuestion.userAnswer)
    } else {
                // 已答题但未提交：根据具体情况决定
                // 如果有图片数据，优先显示查看模式（让用户看到已上传的图片）
                if (currentQuestion.userAnswer && currentQuestion.userAnswer.length > 0) {
                    writeState.showCorrect = false
                    console.log(`📖 非选择题${questionIndex + 1}：已上传图片未提交，查看模式 - 显示已上传图片`)
                    console.log(`🖼️ 图片列表:`, currentQuestion.userAnswer)
                } else {
                    // 标记为已答但没有图片数据，切换到答题模式
        writeState.showCorrect = true
                    console.log(`✏️ 非选择题${questionIndex + 1}：已答题但无图片数据，答题模式`)
                }
            }
        } else {
            // 未答题：答题模式
            writeState.showCorrect = true
            console.log(`📝 非选择题${questionIndex + 1}：未答题，答题模式`)
        }
    }
    
    // 使用nextTick确保DOM更新
    nextTick(() => {
        console.log(`🔄 题目${questionIndex + 1}显示模式已更新:`, {
            当前模式: writeState.showCorrect ? '答题模式' : '查看模式',
            显示内容: writeState.showCorrect ? '上传组件' : '已上传图片',
            图片数量: currentQuestion.userAnswer?.length || 0
        })
    })
}

// 🎯 优化：统一处理选项点击
const handleOptionClick = (optionIndex: number) => {
    console.log('=== 点击选项 ===')
    console.log('点击的选项索引:', optionIndex)
    console.log('当前题目索引:', writeState.current)
    
    if (writeState.current === null) {
        console.log('❌ 当前题目索引为null，无法选择选项')
        return
    }
    
    const currentItem = allTest.value[writeState.current]
    
    // 检查题目是否已提交或已批改（只读模式）
    if (currentItem.submitted === true || currentItem.userMark !== null) {
        console.log('ℹ️ 题目已提交或已批改，仅查看模式')
        // 在已批改的情况下，可以显示答案解析等信息
        if (currentItem.userMark !== null) {
            console.log('📊 查看批改结果:', {
                用户答案: currentItem.ques.userJson,
                正确答案: currentItem.ques.answers,
                批改结果: currentItem.userMark === 1 ? '正确' : currentItem.userMark === 2 ? '半对' : '错误'
            })
        }
        return
    }
    
    // 可以选择答案的情况
    selectOption(optionIndex)
}

// 选择选项方法
const selectOption = (optionIndex: number) => {
    console.log('=== 选择答案选项 ===')
    console.log('点击的选项索引:', optionIndex)
    console.log('当前题目索引:', writeState.current)
    
    if (writeState.current === null) {
        console.log('当前题目索引为null，无法选择选项')
        return
    }
    
    const currentItem = allTest.value[writeState.current]
    
    // 检查题目是否已提交
    if (currentItem.submitted === true) {
        console.log('题目已提交，不允许修改答案')
        return
    }
    
    // 检查题目是否已批改
    if (currentItem.userMark !== null) {
        console.log('题目已批改，不允许修改答案')
        return
    }
    
    console.log('当前题目类型:', currentItem?.ques?.cate === 1 ? '单选题' : '多选题')
    console.log('选择前的用户答案:', currentItem?.ques?.userJson)
    
    if (!currentItem || !currentItem.ques.userJson) {
        currentItem.ques.userJson = []
    }
    
    // 对于单选题，替换选择；对于多选题，切换选择
    if (currentItem.ques.cate === 1) { // 单选题
        currentItem.ques.userJson = [optionIndex]
        console.log('单选题：设置答案为', [optionIndex])
    } else { // 多选题
        const index = currentItem.ques.userJson.indexOf(optionIndex)
        if (index > -1) {
            currentItem.ques.userJson.splice(index, 1)
            console.log('多选题：取消选择', optionIndex, '，当前答案:', currentItem.ques.userJson)
        } else {
            currentItem.ques.userJson.push(optionIndex)
            console.log('多选题：添加选择', optionIndex, '，当前答案:', currentItem.ques.userJson)
        }
    }
    
    console.log('选择后的最终答案:', currentItem.ques.userJson)
    console.log('是否已答题:', Boolean(currentItem.ques.userJson.length))
    
    // 🎯 新增：验证选择题答题时进度条不移动
    const currentProgress = progressPercentage.value
    console.log('📊 选择题答题后进度检查:', {
        当前进度: currentProgress + '%',
        说明: '选择题答题不会移动进度条，只有saveUserJsonApi成功后才移动',
        题目状态: {
            已选择: Boolean(currentItem.ques.userJson.length),
            已提交: currentItem.submitted,
            已批改: currentItem.userMark !== null
        }
    })
    
    console.log('=== 选择答案完成 ===')
}

// 上一题
const goToPrevQuestion = () => {
    if (writeState.current !== null && writeState.current > 0) {
        writeState.current = writeState.current - 1
        writeState.itemTimer = 0
    }
}

// 🎯 优化：智能下一题功能
const goToNextQuestion = () => {
    console.log('=== 点击下一题按钮 ===')
    console.log('当前题目索引:', writeState.current)
    console.log('总题目数量:', allTest.value.length)
    console.log('所有题目已作答:', allQuestionsAnswered.value)
    console.log('还有未作答题目:', hasUnansweredQuestions.value)
    
    if (writeState.current !== null) {
        const currentQuestion = allTest.value[writeState.current]
        const hasAnswer = checkIfHasAnswer(currentQuestion)
        
        console.log('当前题目信息:', {
            题目序号: writeState.current + 1,
            题目类型: currentQuestion?.ques?.cateName,
            用户答案_选择题: currentQuestion?.ques?.userJson,
            用户答案_非选择题: currentQuestion?.userAnswer,
            是否已答题: hasAnswer
        })
        
        // 如果当前题目已答题，先提交
        if (hasAnswer) {
            console.log('检测到已答题，先提交当前题目')
            
            // 🎯 优化：智能查找下一个未作答的题目
            let nextUnansweredIndex:any = null
            for (let i = 0; i < allTest.value.length; i++) {
                if (!checkIfHasAnswer(allTest.value[i])) {
                    nextUnansweredIndex = i
                    break
                }
            }
            
            // 设置标记，表示这是通过"下一题"按钮触发的提交
            writeState.fromNextButton = true
            writeState.expectedNextIndex = nextUnansweredIndex
            
            console.log('期望跳转到下一个未作答题目索引:', nextUnansweredIndex)
            
            handleSubmit(currentQuestion, writeState.current)
        } else {
            // 如果当前题目未答题，直接跳转到下一个未作答的题目
            const nextUnansweredIndex = findNextUnansweredQuestion(writeState.current)
            
            if (nextUnansweredIndex !== null) {
                console.log('当前题目未答题，直接跳转到下一个未作答题目，索引:', nextUnansweredIndex)
                writeState.current = nextUnansweredIndex
                writeState.itemTimer = 0
                
                const nextQuestion = allTest.value[nextUnansweredIndex]
                console.log('跳转到题目信息:', {
                    题目序号: nextUnansweredIndex + 1,
                    题目类型: nextQuestion?.ques?.cateName,
                    用户答案_选择题: nextQuestion?.ques?.userJson,
                    用户答案_非选择题: nextQuestion?.userAnswer,
                    是否已答题: checkIfHasAnswer(nextQuestion)
                })
                
                // 更新答题区域显示模式
                updateAnswerAreaDisplay(nextUnansweredIndex)
            } else {
                console.log('所有题目都已作答，无法跳转')
            }
        }
    } else {
        console.log('当前题目索引为null，无法切换')
    }
    console.log('=== 下一题按钮处理完成 ===')
}

// 🎯 新增：查找下一个未作答的题目
const findNextUnansweredQuestion = (currentIndex: number) => {
    // 从当前题目之后开始查找
    for (let i = currentIndex + 1; i < allTest.value.length; i++) {
        if (!checkIfHasAnswer(allTest.value[i])) {
            return i
        }
    }
    
    // 如果后面没有未作答的题目，从头开始查找
    for (let i = 0; i < currentIndex; i++) {
        if (!checkIfHasAnswer(allTest.value[i])) {
            return i
        }
    }
    
    // 没有找到未作答的题目
    return null
}

// 🎯 新增：自动判断答案是否正确
const checkAnswerCorrectness = (userAnswers: any[], correctAnswers: any[], questionType: number) => {
    console.log('🔍 开始判断答案正确性:', {
        用户答案: userAnswers,
        正确答案: correctAnswers,
        题目类型: questionType === 1 ? '单选题' : questionType === 3 ? '多选题' : '非选择题'
    })
    
    // 非选择题不进行自动判断，返回null（需要人工批改）
    if (questionType !== 1 && questionType !== 3) {
        console.log('📝 非选择题，跳过自动判断')
        return null
    }
    
    // 确保答案数组存在且不为空
    if (!userAnswers || !Array.isArray(userAnswers) || userAnswers.length === 0) {
        console.log('❌ 用户答案为空')
        return 0 // 错误
    }
    
    if (!correctAnswers || !Array.isArray(correctAnswers) || correctAnswers.length === 0) {
        console.log('❌ 正确答案为空')
        return 0 // 错误
    }
    
    // 将答案转换为字符串数组进行比较
    const userAnswersStr = userAnswers.map(answer => String(answer)).sort()
    const correctAnswersStr = correctAnswers.map(answer => String(answer)).sort()
    
    console.log('🔄 答案比较:', {
        用户答案排序: userAnswersStr,
        正确答案排序: correctAnswersStr
    })
    
    // 单选题和多选题的判断逻辑
    if (questionType === 1) {
        // 单选题：答案必须完全一致
        if (userAnswersStr.length === 1 && correctAnswersStr.length === 1) {
            const isCorrect = userAnswersStr[0] === correctAnswersStr[0]
            console.log('📊 单选题判断结果:', isCorrect ? '正确' : '错误')
            return isCorrect ? 1 : 0
        } else {
            console.log('❌ 单选题答案格式错误')
            return 0
        }
    } else if (questionType === 3) {
        // 多选题：答案必须完全一致（包括数量和内容）
        if (userAnswersStr.length === correctAnswersStr.length) {
            const isCorrect = userAnswersStr.every((answer, index) => answer === correctAnswersStr[index])
            console.log('📊 多选题判断结果:', isCorrect ? '正确' : '错误')
            return isCorrect ? 1 : 0
        } else {
            console.log('❌ 多选题答案数量不匹配')
            return 0
        }
    }
    
    console.log('❌ 未知题目类型')
    return 0
}

// 提交当前题目
const handleCurrentSubmit = () => {
    if (writeState.current !== null) {
        const currentItem = allTest.value[writeState.current]
        handleSubmit(currentItem, writeState.current)
    }
}

// 监听当前题目变化
watch(() => writeState.current, (newVal, oldVal) => {
    if (newVal !== oldVal && newVal !== null) {
        // 切换题目时重置单题计时器
        writeState.itemTimer = 0
    }
})

const correcthandle = (index: number, userMark: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    currentItem.userMarks = userMark;
    const data = JSON.parse(JSON.stringify(currentItem))
    data.userMark = userMark;
    const arr = [] as string[]
    data.ques.userJson.map((item: any) => {
        arr.push(item.url)
    })
    data.ques.userJson = arr
    // 在这里不重置计时器，让handleSubmit统一处理
    handleSubmit(data, index)
}
// 非选择题自主批改
const nonSelectFinish = (index: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    const arr = [] as string[]
    currentItem.ques.userJson.map((item: any) => {
        arr.push(item.url)
    })
    currentItem.userAnswer = arr
    writeState.showCorrect = false
    // 注意：这里不重置计时器，因为用户还需要进行自主批改
    // 计时器会在correcthandle调用handleSubmit时重置
}
const checkChange = (val: any, index: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index]?.ques;
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    currentItem.userJson = [val];
}
// 🎯 优化：处理图片上传列表更新，确保下一题按钮正确显示
const handleImgList = (index: number, imgList: any) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    console.log('🖼️ 图片上传更新:', {
        题目索引: index + 1,
        题目类型: currentItem.ques?.cateName,
        上传前userJson: currentItem.ques?.userJson,
        上传前userAnswer: currentItem.userAnswer,
        新图片列表: imgList
    });

    // 🎯 优化：同时更新 userJson 和 userAnswer
    if (currentItem.ques) {
        currentItem.ques.userJson = imgList;
    }
    
    // 将图片列表转换为 userAnswer 格式
    if (imgList && Array.isArray(imgList)) {
        currentItem.userAnswer = imgList.map((item: any) => {
            return typeof item === 'string' ? item : item.url || item
        });
    } else {
        currentItem.userAnswer = [];
    }

    console.log('✅ 图片上传更新完成:', {
        题目索引: index + 1,
        更新后userJson: currentItem.ques?.userJson,
        更新后userAnswer: currentItem.userAnswer,
        是否已作答: checkIfHasAnswer(currentItem)
    });

    // 🎯 优化：强制触发响应式更新
    nextTick(() => {
        console.log('🔄 响应式更新完成，下一题按钮状态:', {
            题目索引: index + 1,
            是否已作答: checkIfHasAnswer(currentItem),
            下一题按钮应显示: checkIfHasAnswer(currentItem) ? '高亮' : '禁用'
        });
    });
}
//过滤修改题干标签内容
const resetSty = function (testItem: any, sort?: number) {
  const tittle = "（" + testItem.ques.cateName + "）" +sort + "." + '&nbsp;&nbsp;' + filterContent(testItem.ques.content)
  return tittle
}
//过滤掉填空题等div标签的contenteditable属性
const filterContent = (contect: string) => {
  let highlightedResult = ""
  highlightedResult = contect.replaceAll('contenteditable="true"', " ")
  highlightedResult = highlightedResult.replaceAll("contenteditable='true'", " ")
  return highlightedResult
}
//过滤修改选项内容
const resetOptions = function (testItem: any) {
    let optionHtml = ""
    if (!testItem.ques.options) return
    testItem.ques.options.map((item: any, index: number) => {
        optionHtml += `<div class="answer-item"> ${String.fromCharCode(65 + index)}. <div style="display:inline-table;max-width:39.8125rem">${item}</div></div>`
    })
    return optionHtml
}

// 🎯 优化：下一题提交处理，包含自动答案判断
const handleSubmit = (data: any, index: number) => {
    console.log(data.ques,"datadatadatadata398")
    // 计算单题训练时长 - 修改为使用独立计时器
    let userMark = data.ques.userMark
    let userJsons = data.ques.userJson 
    console.log(userJsons,"userJsonsuserJsonsuserJsons899999999999999",userMark)
    
    // 🎯 新增：自动判断答案正确性（仅对选择题）
    if (data.ques.cate === 1 || data.ques.cate === 3) {
        // 选择题：自动判断答案是否正确
        const autoUserMark = checkAnswerCorrectness(
            userJsons, 
            data.ques.answers || [], 
            data.ques.cate
        )
        
        // 如果自动判断成功，使用自动判断结果
        if (autoUserMark !== null) {
            userMark = autoUserMark
            console.log('🤖 自动判断结果:', {
                题目类型: data.ques.cate === 1 ? '单选题' : '多选题',
                用户答案: userJsons,
                正确答案: data.ques.answers,
                判断结果: userMark === 1 ? '正确' : userMark === 2 ? '半对' : '错误',
                userMark值: userMark
            })
        } else {
            console.log('⚠️ 自动判断失败，保持原userMark值:', userMark)
        }
    } else {
        // 非选择题：不进行自动判断，保持原userMark值
        console.log('📝 非选择题，跳过自动判断，userMark保持:', userMark)
    }
    
    if (data.ques.cate != 1 && data.ques.cate != 3) {
        userJsons = data.ques.userJson.map((item: any) => { return item.url })
    }
    
    const params = {
        trainingItemId: data.trainingItemId,
        trainTime: timeState.seconds * 1000,
        trainItemTime: writeState.itemTimer * 1000, // 使用独立的单题计时器
        userJson: userJsons,
        userMark: userMark,
        noteSource: data.ques.noteSource,
        cate: data.ques.cate
    }
    
    // 🎯 优化：选择题也传递userMark参数（因为我们已经自动判断了）
    // if(data.ques.cate == 1 || data.ques.cate == 3) {
    //     delete params.userMark
    // }
    writeState.loading = true

    saveUserJsonApi(params)

    .then((res: any) => {
        // ElMessage({
        //     message: '批改成功',
        //     type: 'success'
        // })
        writeState.itemTimer = 0 // 重置单题计时器
        
        // 🎯 优化：对于选择题，使用我们自动判断的userMark；对于非选择题，使用服务器返回的值
        if (data.ques.cate === 1 || data.ques.cate === 3) {
            // 选择题：使用自动判断的userMark
            console.log('📊 选择题使用自动判断的userMark:', userMark)
        } else {
            // 非选择题：使用服务器返回的userMark
            userMark = res.data
            console.log('📊 非选择题使用服务器返回的userMark:', userMark)
        }
        
        // 手动更新allTest数据，避免调用getDetails
        // 更新当前题目的状态
        if (allTest.value[index]) {
            allTest.value[index].userMark = userMark
            allTest.value[index].submitted = true  // 标记为已提交
            console.log('题目已标记为已提交状态, 题目索引:', index + 1, 'userMark:', userMark)
            
            // 🎯 优化：正确设置用户答案，确保题号背景色显示
            if (data.ques.cate == 1 || data.ques.cate == 3) {
                // 选择题：将userJson转换为userAnswer
                allTest.value[index].userAnswer = data.ques.userJson.map((item: any) => item.toString())
                console.log(`✅ 选择题${index + 1}答案已保存:`, allTest.value[index].userAnswer)
            } else {
                // 非选择题：确保userAnswer包含上传的图片URL
                if (data.ques.userJson && data.ques.userJson.length > 0) {
                    allTest.value[index].userAnswer = data.ques.userJson.map((item: any) => {
                        return typeof item === 'string' ? item : item.url
                    })
                    console.log(`✅ 非选择题${index + 1}图片答案已保存:`, allTest.value[index].userAnswer)
                }
                writeState.showCorrect = true
            }
        }

        // 🎯 强制触发题号背景色更新
        nextTick(() => {
            console.log('🎨 题目提交后，触发题号背景色更新检查')
            const hasAnswer = checkIfHasAnswer(allTest.value[index])
            const colorStatus = setClass1(allTest.value[index], index)
            console.log(`题目${index + 1}状态更新:`, {
                题目类型: allTest.value[index].ques?.cateName,
                是否已答题: hasAnswer,
                是否已提交: allTest.value[index].submitted,
                是否已批改: allTest.value[index].userMark !== null,
                题号背景色: colorStatus,
                userAnswer长度: allTest.value[index].userAnswer?.length || 0
            })
            
            // 🎯 新增：触发进度条更新检查
            const currentProgress = progressPercentage.value
            console.log('🚀 saveUserJsonApi成功，进度条将更新:', {
                当前进度: currentProgress + '%',
                触发原因: 'saveUserJsonApi成功',
                题目索引: index + 1
            })
            
            // 🎯 调试：显示所有题目的最新状态
            setTimeout(() => {
                debugQuestionStatus()
            }, 50)
        })

        // 🎯 优化：智能查找下一个未作答的题目
        let nextQuestionIndex:any = null
        
        // 如果是通过"下一题"按钮触发的提交，使用指定的下一题索引
        if (writeState.fromNextButton && writeState.expectedNextIndex !== undefined) {
            nextQuestionIndex = writeState.expectedNextIndex
            console.log('使用下一题按钮指定的索引:', nextQuestionIndex)
            // 清除标记
            writeState.fromNextButton = false
            writeState.expectedNextIndex = undefined
        } else {
            // 🎯 优化：查找下一个未作答的题目（而不是未批改的题目）
            nextQuestionIndex = findNextUnansweredQuestion(index)
            console.log('使用智能查找的下一题索引:', nextQuestionIndex)
        }
        
        // 更新当前题目索引
        writeState.current = nextQuestionIndex

        // 🎯 切换到下一题时，设置答题区域显示模式
        if (nextQuestionIndex !== null) {
            nextTick(() => {
                updateAnswerAreaDisplay(nextQuestionIndex)
                console.log(`🔄 已切换到题目${nextQuestionIndex + 1}，显示模式已更新`)
            })
        }

        // 如果所有题目都已完成，停止计时器并启用提交按钮
        if (nextQuestionIndex === null) {
            if (timer !== null) {
                clearInterval(timer)
                timer = null
            }
            writeState.disabled = false
        }

        writeState.loading = false
        console.log('writeState.current', writeState.current)
    })
    .catch(() => {
        writeState.loading = false
    })
}
// 🎯 优化：完成提交，确保最后一题也调用saveUserJson接口
const submit = async () => {
    console.log("点击完成",detailData)
    console.log("点击完成123")
    
    // 🎯 优化：如果当前还有题目未提交，先提交当前题目
    if (writeState.current !== null) {
        const currentItem = allTest.value[writeState.current]
        const hasAnswer = checkIfHasAnswer(currentItem)
        
        if (hasAnswer) {
            console.log('🔄 检测到最后一题已作答，先提交当前题目')
            try {
                // 直接调用handleSubmit，但不等待完成
                handleSubmit(currentItem, writeState.current)
                
                // 等待一小段时间让提交完成
                await new Promise(resolve => setTimeout(resolve, 1000))
                
                console.log('✅ 最后一题提交完成')
            } catch (error) {
                console.error('❌ 最后一题提交失败:', error)
            }
        } else {
            console.log('⚠️ 最后一题未作答，跳过提交')
        }
    }
    
    // 🎯 新增：检查题目类型，判断是否有主观题需要批改
    const subjectiveQuestions = allTest.value.filter((item: any) => {
        const cate = item.ques?.cate
        return cate !== 1 && cate !== 3 // 非选择题（非单选题和多选题）
    })
    
    writeState.subjectiveNum = subjectiveQuestions.length
    
    console.log('🔍 题目类型检查:', {
        总题数: allTest.value.length,
        选择题数量: allTest.value.filter(item => item.ques?.cate === 1 || item.ques?.cate === 3).length,
        主观题数量: subjectiveQuestions.length,
        主观题类型: subjectiveQuestions.map(item => ({
            题目: allTest.value.indexOf(item) + 1,
            类型: item.ques?.cateName,
            cate: item.ques?.cate
        }))
    })
    
    // 🎯 优化：确保点击完成按钮后显示最后一题
    writeState.current = allTest.value.length - 1
    console.log('🎯 点击完成按钮，设置当前题目为最后一题:', writeState.current + 1)
    
    // 🎯 新增：如果有主观题，显示批改弹窗
    if (subjectiveQuestions.length > 0) {
        console.log('📝 检测到主观题，显示批改弹窗')
        writeState.showDialog = true
        writeState.btnloading = false
        return // 停止执行，等待用户批改
    }
    
    console.log('✅ 所有题目都是选择题，直接进入正常提交流程')
    writeState.btnloading = true
    
    // 执行提交流程
    await executeSubmit()
}

// 🎯 新增：执行提交流程的独立函数
const executeSubmit = async () => {
    // 🎯 优化：构建提交数据，确保包含所有题目的最新状态
    const items = allTest.value.map((item: any, index: number) => {
        const itemData = {
            // cate: item.ques.cate,
            trainingItemId: item.trainingItemId,
            userJson: item.userAnswer || [],
            userMark: item.userMark,
            trainItemTime: item.trainTime || 0
        }
        
        console.log(`📊 题目${index + 1}提交数据:`, {
            题目类型: item.ques?.cate === 1 ? '单选题' : item.ques?.cate === 3 ? '多选题' : '非选择题',
            用户答案: itemData.userJson,
            批改结果: itemData.userMark,
            训练时长: itemData.trainItemTime,
            是否已提交: item.submitted
        })
        
        return itemData
    })
    
    // 🎯 优化：在第1645行打印最后一条数据存储状态
    console.log('📋 所有题目提交数据汇总:', {
        总题数: items.length,
        已提交题数: items.filter(item => item.userMark !== null).length,
        未提交题数: items.filter(item => item.userMark === null).length,
        最后一条数据: items[items.length - 1],
        数据存储状态: items[items.length - 1]?.userMark !== null ? '✅ 已存储成功' : '❌ 没有存储成功'
    })

        // 确保使用最新的计时值
        const finalTrainTime = timer !== null ? timeState.seconds * 1000 : convertTimeToMilliseconds();
        console.log(queryData.bookId,"queryData.bookIdqueryData.bookIdqueryData.bookIdqueryData.bookId")
        const params = {
            trainingId: detailData.trainingId,   //训练id
            trainTime: finalTrainTime, // 使用最新的时间  训练时长
            status: 2,   //训练状态  1:未完成  2:已完成
            bookId: queryData.bookId,   //书籍id
            chapterId:queryData.chapterId,   //章节id
            level:queryData.level,   //级别  1:青铜  2:白银  3:黄金 4:钻石
            items:items
        }
        const params2 = {
            trainingId: detailData.trainingId,   //训练id
            trainTime: finalTrainTime, // 使用最新的时间  训练时长
            status: 2,   //训练状态  1:未完成  2:已完成
        }

        // 根据source来源调用不同的API
        if(queryData.source === 'ripe') {
            console.log('调用知识点标熟API - saveTrainingApi')
            // 知识点标熟答题保存
            saveTrainingApi(params2)
            .then((res: any) => {
                console.log(res,"saveTrainingApi - 知识点标熟保存成功")
                if (timer !== null) { // 添加类型安全检查
                    clearInterval(timer)
                    timer = null // 确保timer被清空
                }
                // 重置计时器
                writeState.itemTimer = 0

                writeState.btnloading = false
                return;
                router.push({
                    path: '/ai_percision/foundation_report',
                    query: {
                        data: dataEncrypt({
                        reportId: detailData.trainingId,
                        pageSource: '1',
                        source: queryData.source, // 传递source标识
                        type:queryData.type,
                        subject:queryData.subject,
                        bookId:queryData.bookId,
                        subjectEn:queryData.subjectEn,
                        curChapterId:queryData.curChapterId
                        }),
                    }
                })
            })
            .catch(() => {
                writeState.btnloading = false
            })
        } else {
            console.log('调用精准学API - savePointTrainingApi')
            // 精准学答题保存
            savePointTrainingApi(params)
            .then((res: any) => {
                console.log(res,"savePointTrainingApi - 精准学保存成功")
                if (timer !== null) { // 添加类型安全检查
                    clearInterval(timer)
                    timer = null // 确保timer被清空
                }
                // 重置计时器
                writeState.itemTimer = 0

                writeState.btnloading = false
                return;
                
                router.push({
                    path: '/ai_percision/foundation_report',
                    query: {
                        data: dataEncrypt({
                            reportId: detailData.trainingId,
                            pageSource: '1',
                            type:queryData.type,
                            subject:queryData.subject,
                            subjectEn:queryData.subjectEn,
                            curChapterId:queryData.curChapterId,
                            bookId:queryData.bookId,
                            selectedChildIds:queryData.selectedChildIds,
                            learn:queryData.learn,
                        }),
                    }
                })
            })
            .catch(() => {
                writeState.btnloading = false
            })
        }

        // saveToIntroduceddApi(params)
        // .then((res: any) => {
        //     if (timer !== null) { // 添加类型安全检查
        //         clearInterval(timer)
        //         timer = null // 确保timer被清空
        //     }
        //     // 重置计时器
        //     writeState.itemTimer = 0

        //     writeState.btnloading = false
        //     router.push({
        //         path: '/ai_percision/entrance_assessment/test_report',
        //         query: {
        //             data: dataEncrypt({
        //             reportId: detailData.trainingId,
        //             pageSource: '1'
        //             }),
        //         }
        //     })
        // })
        // .catch(() => {
        //     writeState.btnloading = false
        // })

}
// 获取训练报告数据
// const getTringDetail = () => {
//     // 生成诊断报告弹窗暂未加上

//     getDetailsApi({trainingId: writeState.trainingId}).then((res: any) => {
//         if (res.code == 200) {
//             const data = res.data
//             localStorage.setItem('diagnosticReport', JSON.stringify(data))
//             writeState.btnloading = false
//             writeState.jfNum = res.data.integral
//             writeState.jfShow = true
//             writeState.jfHide = false
//             setTimeout(()=>{
//                 router.push({
//                     path: '/ai_percision/knowledge_graph_detail/training_report',
//                     query: {
//                         data: dataEncrypt({
//                             sourceId: queryData.sourceId,
//                             showStep: '1'
//                         })
//                     }
//                 })
//             },3000)
//         }
//     }).catch((error) => {
//     })
// }
const handleAnalysis = () => {
    router.push({
        path: '/ai_percision/knowledge_graph_detail/paper_analysis'
    })
}
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    position: relative;
    width: 100%;
    background-image: url('@/assets/img/percision/training/dtbj.png');
    background-size: 100%;
    background-repeat: no-repeat;
    padding-top: 60px;
    // height: 700px;
    .top-nav{
        background-image: url('@/assets/img/percision/training/top_back.png');
        background-size: 100%;
        background-repeat: no-repeat;
        display: flex;
        height: 95px;
        align-items: center;
        position: relative;
        
        .exit-btn{
            width: 100px;
            height: 31px;
            margin-left: 30px;
            cursor: pointer;
        }
        
        // 🎯 新增：答题进度条样式
        .progress-container {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 10px;
            
            .progress-bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(222, 229, 234, 1);
                border-radius: 0;
            }
            
            .progress-fill {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                background-color: rgba(255, 196, 159, 1);
                border-radius: 0;
                transition: width 0.3s ease;
                z-index: 1;
            }
            
            .progress-icon {
                position: absolute;
                top: -15px; // 向上偏移，使图标在进度条上方
                width: 40px;
                height: 40px;
                transform: translateX(-50%); // 居中对齐
                transition: left 0.3s ease-in-out;
                z-index: 2;
                pointer-events: none; // 防止影响其他元素的点击
                
                // 添加一些视觉效果
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            }
        }
    }
    .five-step-box {
        position: absolute;
        right: -8.75rem;
        top: 11.25rem;
    }
    .left {
        flex: 1;
        .test-content {
            // height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
            box-sizing: border-box;
            overflow-y: auto;
                background: #ffffff;
            // border-radius: 12px;
            border-radius: 0 0 0px 20px;
            
            .single-question-container {
                padding:20px;
                height: 100%;
                display: flex;
                flex-direction: column;
                
                
                .question-cont {
                    height: 300px;
                    overflow-y: auto;
                    padding-right: 10px;
                    margin-bottom: 20px;
                    
                    /* 自定义滚动条样式 */
                    &::-webkit-scrollbar {
                        width: 6px;
                    }
                    
                    &::-webkit-scrollbar-track {
                        background: #f1f1f1;
                        border-radius: 3px;
                    }
                    
                    &::-webkit-scrollbar-thumb {
                        background: #c1c1c1;
                        border-radius: 3px;
                        
                        &:hover {
                            background: #a8a8a8;
                        }
                    }
                }
                
                .question-header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 4px;
                    
                    .question-type {
                        background: rgba(238, 242, 253, 1);
                        color: rgba(90, 133, 236, 1);
                        padding: 0.25rem 0.75rem;
                        border-radius: 4px;
                        font-size: 16px;
                        font-weight: 700;
                        margin-right: 1rem;
                        flex-shrink: 0;
                    }
                    
                    .question-number {
                        font-size: 20px;
                        font-weight: 700;
                        color: #1f2937;
                        flex-shrink: 0;
                    }
                }
                
                .question-content {
                    font-size: 1rem;
                    line-height: 1.6;
                    color: rgba(42, 43, 42, 1);
                    margin-bottom: 10px;
                    flex: 1;
                }
                
                .question-options {
                    margin-bottom: 1rem;
                    
                    .option-item {
                        display: flex;
                        align-items: flex-start;
                        padding: 10px 15px;
                        border-radius: 8px;
                        
                        .option-label {
                            font-weight: 600;
                            margin-right: 0.75rem;
                            color: #374151;
                            min-width: 1.5rem;
                        }
                        
                        .option-content {
                            flex: 1;
                            color: #374151;
                        }
                    }
                }
                
                .answer-section {
                    background-image: url('@/assets/img/percision/training/ztqy.png');
                    background-size: 100%;
                    background-repeat: no-repeat;
                    height: 248px;
                position: relative;
                
                    
                    .choice-answer-area {
                        .answer-options {
                            margin: 20px;
                            display: flex;
                            flex-wrap: wrap;
                            gap: 1rem;
                            
                            .answer-option {
                                display: flex;
                                align-items: center;
                                padding: 0.75rem 1.5rem;
                                border: 2px solid #e5e7eb;
                                border-radius: 8px;
                                cursor: pointer;
                                transition: all 0.2s ease;
                                background: #ffffff;
                                
                                &:hover:not(.disabled) {
                                    border-color: #3b82f6;
                                    background: #f0f9ff;
                                }
                                
                                &.disabled {
                                    cursor: not-allowed;
                                    opacity: 0.6;
                                    background: #f5f5f5;
                                    border-color: #d1d5db;
                                    
                                    &:hover {
                                        border-color: #d1d5db;
                                        background: #f5f5f5;
                                    }
                                }
                                
                                &.selected {
                                    border-color: #3b82f6;
                                    background: #dbeafe;
                                    
                                    .option-checkbox {
                                        background: #3b82f6;
                                        border-color: #3b82f6;
                                        
                                        .checkmark {
                                            color: white;
                                        }
                                    }
                                }
                                
                                .option-checkbox {
                                    width: 20px;
                                    height: 20px;
                                    border: 2px solid #d1d5db;
                                    border-radius: 4px;
                                    margin-right: 0.75rem;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    transition: all 0.2s ease;
                                    
                                    .checkmark {
                                        font-size: 12px;
                                        font-weight: bold;
                                    }
                                }
                                
                                .option-letter {
                                    font-weight: 600;
                                    color: #374151;
                                }
                            }
                        }
                    }
                }
                
                .question-footer {
                    margin-top: auto;
                    padding-top: 2rem;
                    
                    .navigation-buttons {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        
                        .submit-btn {
                            padding: 0.75rem 2rem;
                            background: #10b981;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 500;
                            transition: all 0.2s ease;
                            
                            &:hover {
                                background: #059669;
                            }
                        }
                    }
                }
            }
        }
    }
    .right {
        padding: 1.125rem 0 0 0 ;
        width: 280px;
        flex-shrink: 0;
        background: #f5f8ff;
        box-sizing: border-box;
        border-radius: 0 0px 20px 0px;
        background: url('@/assets/img/percision/training/dtbjt.png') no-repeat;
        background-size: 100% 100%;
        // height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
        .time-box {
            // display: flex;
            align-items: center;
            color: #2a2b2a;
            font-size: 1.875rem;
            font-weight: 700;
            padding-left: .625rem;
            padding-bottom: 1.25rem;
            border-bottom: .0625rem dashed #eaeaea;
            .time-text {
                display: flex;
                align-items: center;
                margin-left: 10px;
                font-size: 16px;
                font-weight: 700;
                margin-bottom: 20px;
                img{
                    width: 21px;
                    height: 21px;
                    margin-right: 8px;
                }
            }
             .time-bg{
                 background: url('@/assets/img/percision/training/timebg.png') no-repeat;
                 background-size: 100% 100%;
                 background-position: center;
                 width: 220px;
                 height: 60px;
                 display: flex;
                 align-items: center;
                 justify-content: center;
                 margin: 0 auto;
                .time-number {
                    width: 36px;
                    height: 36px;
                    line-height: 36px;
                    font-size: 20px;
                    font-weight: 700;
                    text-align: center;
                    border-radius: .25rem;
                    border: .0625rem solid #eaeaea;
                    background: #5A85EC;
                    margin: 0 .625rem;
                    color: #fff;
                }
            }
           
        }
        .test-number-box {
            // height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 8.125rem);
            box-sizing: border-box;
            padding: 1.25rem .625rem 0 0;
            display: flex;
            flex-direction: column;
            margin-left: 10px;
            .question-nav-title {
                font-size: 1rem;
                font-weight: 600;
                color: #374151;
                margin-bottom: 1rem;
                padding-left: .625rem;
                display: flex;
                align-items: center;
                img{
                    width: 21px;
                    height: 21px;
                    margin-right: 8px;
                }
            }
            
            .question-nav-grid {
                height: 248px;
                overflow-y: auto;
                overflow-x: hidden;
                padding-right: 8px;
                
                /* 自定义滚动条样式 */
                &::-webkit-scrollbar {
                    width: 6px;
                }
                
                &::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 3px;
                }
                
                &::-webkit-scrollbar-thumb {
                    background: #c1c1c1;
                    border-radius: 3px;
                    
                    &:hover {
                        background: #a8a8a8;
                    }
                }
                
                /* Firefox 滚动条样式 */
                scrollbar-width: thin;
                scrollbar-color: #c1c1c1 #f1f1f1;
                
            .test-number-item {
                display: inline-block;
                margin-left: 20px;
                margin-bottom: .625rem;
                border-radius: .25rem;
                width: 2.5rem;
                height: 2.5rem;
                line-height: 2.5rem;
                text-align: center;
                color: #2a2b2a;
                font-size: 1rem;
                font-weight: 400;
                border: .0625rem solid #eaeaea;
                background: #fff;
                box-sizing: border-box;
                cursor: pointer;
                transition: all 0.2s ease;
                
                &:hover {
                    background: #e5e7eb;
                    border-color: #9ca3af;
                }
            }
            }
            
            .submit-section {
                margin-top: 1rem;
                padding-top: 1rem;
                padding: 0 10px;
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-bottom: 10px;
                border-bottom: 1px dashed rgba(221, 221, 221, 1);
                .next-question-btn,
                .finish-btn {
                    width: 136px;
                    height: 50px;
                    border: none;
                    border-radius: 20px;
                    background: rgba(42, 43, 42, 1);
                    color: #ffffff;
                    font-size: 18px;
                    font-weight: 700;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-bottom: 10px;
                    
                    &:hover:not(.disabled) {
                        background: rgba(90, 133, 236, 1);
                    }
                    
                    &.disabled {
                        background: rgba(221, 221, 221, 1);
                        color: rgba(153, 153, 153, 1);
                        cursor: not-allowed;
                        
                        &:hover {
                            background: rgba(221, 221, 221, 1);
                            color: rgba(153, 153, 153, 1);
                        }
                    }
                }
                
                .finish-btn {
                    background: #10b981;
                    
                    &:hover {
                        background: #059669;
                    }
                }
            }
            
            .color-type {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 15px;
                padding: 14px 10px;
                
                .legend-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 5px;
                    
                    .legend-square {
                        width: 14px;
                        height: 14px;
                        border-radius: 2px;
                        border-radius: 4px;
                        &.undone {
                            background: rgba(255, 255, 255, 1);
                            border: 1px solid #eaeaea;
                        }
                        
                        &.done {
                            background: rgba(90, 133, 236, 1);
                        }
                        
                        &.correct {
                            background: rgba(0, 201, 163, 1);
                        }
                        
                        &.half-correct {
                            background: rgba(241, 190, 33, 1);
                        }
                        
                        &.wrong {
                            background: rgba(221, 42, 42, 1);
                        }
                    }
                    
                    span {
                        font-size: 12px;
                        color: #666666;
                        font-weight: 400;
                        text-align: center;
                    }
                }
            }
            }
        }
        
            .disabled {
                background: #bebebe;
                cursor: not-allowed;
            }
            .blue {
                background: #5a85ec!important;
                color: #ffffff!important;
            }
            .submitted {
                background: rgba(90, 133, 236, 1)!important;
                color: #ffffff!important;
            }
            .red {
                background: #dd2a2a!important;
                color: #ffffff!important;
            }
            .green {
                background: #00c9a3!important;
                color: #ffffff!important;
            }
            .yellow {
                background: #f1be21!important;
                color: #ffffff!important;
            }
            .size285 {
                width: 17.8125rem;
                height: 2.75rem;
                font-size: 1rem;
                font-weight: 700;
                margin-left: .625rem;
                margin-top: 1.25rem;
                img {
                    width: 1rem;
                    height: 1rem;
        }
    }
}
.icon-btn {
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        margin-right: .3125rem;
    }
}
.answer-img-box {
    padding-left: 1.875rem;
    padding-top: 1.25rem;
    .answer-img {
        width: 144px;
        height: 144px;
        border-radius: .25rem;
        margin-right: .625rem;
    }
}
:deep(.el-checkbox-group) {
    .el-checkbox {
        width: 6.25rem;
        height: 3.125rem;
        margin-right: 1.25rem;
        display: inline-flex;
        justify-content: center;
    }
    .is-checked {
        .el-checkbox__inner {
            background: #5a85ec;
            border: .0625rem solid #5a85ec;
        }
        .el-checkbox__label {
            color: #5a85ec;
        }
    }
    .el-checkbox__inner {
        &:hover {
            border: .0625rem solid #5a85ec;
        }
    }
}
.show-analyse {
    width: 100%;
    background: #fef8e9;
    padding-left: 1.875rem;
    height: 2.1875rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    color: #666666;
    font-size: .75rem;
    font-weight: 400;
    margin-top: 1.25rem;
    span {
        margin-left: .375rem;
    }
}
.analyse {
    padding: .625rem 1.875rem;
    letter-spacing: .125rem;
    background: #fef8e9;
    div {
      margin-bottom: .625rem;
    }
}
.flex-sty {
  display: flex;
  font-size: .875rem;
  align-items: baseline;
  div {
    max-width: 52.375rem;
    line-height: 1.0625rem;
  }
  span {
    text-wrap: nowrap;
    font-weight: 700;
    letter-spacing: normal;
  }
}
.paper-content-ques {
    margin-top: 20px;
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    // 覆盖组件内的按钮样式
    :deep(.camera-container) {
        padding-right: 0;
        flex-direction: row;
        gap: 10px;
        
        .upload-btn {
            width: 101px !important;
            height: 37px !important;
            padding: 0 !important;
            // border: 1px solid rgba(90, 133, 236, 1) !important;
            border-radius: 4px !important;
            background: rgba(238, 242, 253, 1)!important;
            color: rgba(90, 133, 236, 1) !important;
            border: none;
            font-size: 14px !important;
            font-weight: 400 !important;
            margin-bottom: 0 !important;
            margin-left: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            
            &:hover {
                background: rgba(90, 133, 236, 0.1) !important;
            }
        }
    }
    
    :deep(.upload-container) {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: flex-start;
        position: relative;
        
        .img-box {
            width: 140px !important;
            height: 140px !important;
            margin-right: 0 !important;
        }
        
        .upload-box {
            position: static !important;
            padding: 20px;
            
            .upload-btn {
                width: 101px !important;
                height: 37px !important;
                padding: 0 !important;
                border-radius: 4px !important;
                background: rgba(238, 242, 253, 1) !important;
                color: rgba(90, 133, 236, 1) !important;
                font-size: 14px !important;
                font-weight: 400 !important;
                position: static !important;
                width: 101px !important;
                display: flex;
                align-items: center;
                justify-content: center;
                
                &:hover {
                    background: rgba(90, 133, 236, 0.1) !important;
                }
            }
        }
    }
    
    .upload-images {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        
        .upload-image-item {
            width: 140px;
            height: 140px;
            border: 1px solid rgba(221, 221, 221, 1);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
            
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }
}
.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    text-align: center;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    margin-right: .625rem;
    font-weight: 400;
}
.black-text {
    color: black!important;
}
.red-border {
    border: .0625rem solid #dd2a2a;
    color: black!important;
    .squre {
        background: #dd2a2a!important;
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-border {
    color: black!important;
    border: .0625rem solid #00C9A3;
    .squre {
        background: #00C9A3!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.yellow-border {
    color: black!important;
    border: .0625rem solid #f1be21;
    .squre {
        background: #f1be21!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-box {
    border: .0625rem solid #00c9a3!important;
    background: #e5f9f6!important;
    div {
        background-image: url(@/assets/img/percision/right-check.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.red-box {
    border: .0625rem solid #dd2a2a!important;
    background: #fce9e9!important;
    div {
        background-image: url(@/assets/img/percision/wrong-check.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.yellow-box {
    border: .0625rem solid #f1be21!important;
    background: #fef8e8!important;
    div {
        background-image: url(@/assets/img/percision/harf-right.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.answers {
    display: flex;
    margin-top: 1.25rem;
    .answer-box {
        width: 6.25rem;
        height: 3.125rem;
        border-radius: .25rem;
        cursor: pointer;
        border: .0625rem solid #dddddd;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.25rem;
        &:first-child {
            margin-left: 1.875rem;
        }
        div {
            border: .0625rem solid #999999;
            width: 1rem;
            height: 1rem;
            border-radius: .125rem;
            margin-right: .625rem;
        }
    }
}
.black-text {
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: 400;
    text-align: center;
}
.grey-text {
    color: #999999;
    font-size: .875rem;
    font-weight: 400;
    text-align: center;
}

.dialog-footer {
    margin-top: 9.375rem;
    display: flex;
    justify-content: center;
}
.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    cursor: pointer;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    text-align: center;
}
.gery-text {
    text-align: center;
    color: #999999;
}

/* 🎯 新增：主观题批改界面样式 */
.subjective-correction-container {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.question-info-section {
    margin-bottom: 20px;
}

.question-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 15px;
}

.question-type {
    background: rgba(238, 242, 253, 1);
    color: rgba(90, 133, 236, 1);
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 700;
}

.question-number {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
}

.user-answer-section, .correction-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.section-title {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 600;
    color: #374151;
    gap: 10px;
}

.image-count {
    font-size: 14px;
    color: #6b7280;
    font-weight: 400;
}

.current-result {
    font-size: 14px;
    color: #059669;
    font-weight: 500;
}

.answer-img-box {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.no-answer {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 20px;
}

.answers {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.answer-box {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #ffffff;
    font-weight: 500;
    color: #374151;
}

.answer-box:hover {
    border-color: #3b82f6;
    background: #f0f9ff;
}

.answer-box.selected {
    border-color: #059669;
    background: #ecfdf5;
    color: #065f46;
}

.answer-box div {
    width: 16px;
    height: 16px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    margin-right: 8px;
    transition: all 0.2s ease;
}

.answer-box.selected div {
    background: #059669;
    border-color: #059669;
}

.normal-answer-mode {
    /* 正常答题模式的样式 */
}
</style>
<style lang="scss">
.answer-item:not(:last-child) {
    margin-bottom: 1.875rem;
}

.dialog-correct {
    width: 34.875rem!important;
    border-radius: 1.25rem;
    box-sizing: border-box;
}
</style>
